{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\Pavement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Container, Card, Button, Form, Tabs, Tab, Alert, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport './Pavement.css';\nimport useResponsive from '../hooks/useResponsive';\nimport VideoDefectDetection from '../components/VideoDefectDetection';\nimport { FaArrowLeft, FaArrowRight } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Pavement = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('detection');\n  const [detectionType, setDetectionType] = useState('all');\n  const [imageFiles, setImageFiles] = useState([]);\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\n  const [imageLocationMap, setImageLocationMap] = useState({});\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [processedImage, setProcessedImage] = useState(null);\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [locationPermission, setLocationPermission] = useState('unknown');\n  const [locationError, setLocationError] = useState('');\n  const [locationLoading, setLocationLoading] = useState(false);\n\n  // Add state for batch processing results\n  const [batchResults, setBatchResults] = useState([]);\n  const [batchProcessing, setBatchProcessing] = useState(false);\n  const [processedCount, setProcessedCount] = useState(0);\n\n  // Add state for storing processed images for results table\n  const [processedImagesData, setProcessedImagesData] = useState({});\n\n  // Add state for classification error modal\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\n  const [classificationError, setClassificationError] = useState('');\n  const [totalToProcess, setTotalToProcess] = useState(0);\n\n  // Add state for image modal\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [selectedImageData, setSelectedImageData] = useState(null);\n\n  // Add state for image status table filtering\n  const [imageFilter, setImageFilter] = useState('all'); // 'all', 'road', 'non-road'\n\n  // Add state for auto-navigation through results\n  const [autoNavigationActive, setAutoNavigationActive] = useState(false);\n  const [autoNavigationIndex, setAutoNavigationIndex] = useState(0);\n  const autoNavigationRef = useRef(null);\n\n  // Add state for road classification toggle (default to false for better user experience)\n  const [roadClassificationEnabled, setRoadClassificationEnabled] = useState(false);\n\n  // Add state for enhanced detection results table\n  const [detectionTableFilter, setDetectionTableFilter] = useState('all'); // 'all', 'potholes', 'cracks', 'kerbs'\n  const [showDetailedResults, setShowDetailedResults] = useState(false);\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: 'asc'\n  });\n\n  // Auto-clear is always enabled - no toggle needed\n\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n\n  // Create the popover content\n  const reminderPopover = /*#__PURE__*/_jsxDEV(Popover, {\n    id: \"reminder-popover\",\n    style: {\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Popover.Header, {\n      as: \"h3\",\n      children: \"\\uD83D\\uDCF8 Image Upload Guidelines\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popover.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: \"Please ensure your uploaded images are:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          marginBottom: '0',\n          paddingLeft: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Focused directly on the road surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Well-lit and clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Showing the entire area of concern\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Taken from a reasonable distance to capture context\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n\n  // Safari-compatible geolocation permission check\n  const checkLocationPermission = async () => {\n    if (!navigator.permissions || !navigator.permissions.query) {\n      // Fallback for older browsers\n      return 'prompt';\n    }\n    try {\n      const permission = await navigator.permissions.query({\n        name: 'geolocation'\n      });\n      return permission.state;\n    } catch (err) {\n      console.warn('Permission API not supported or failed:', err);\n      return 'prompt';\n    }\n  };\n\n  // Safari-compatible geolocation request\n  const requestLocation = () => {\n    return new Promise((resolve, reject) => {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by this browser'));\n        return;\n      }\n\n      // Check if we're in a secure context (HTTPS)\n      if (!window.isSecureContext) {\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\n        return;\n      }\n      const options = {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        // 15 seconds timeout\n        maximumAge: 60000 // Accept cached position up to 1 minute old\n      };\n      navigator.geolocation.getCurrentPosition(position => {\n        resolve(position);\n      }, error => {\n        let errorMessage = 'Unable to retrieve location';\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\n            break;\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information is unavailable. Please try again.';\n            break;\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out. Please try again.';\n            break;\n          default:\n            errorMessage = `Location error: ${error.message}`;\n            break;\n        }\n        reject(new Error(errorMessage));\n      }, options);\n    });\n  };\n\n  // Enhanced location handler with Safari-specific fixes\n  const handleLocationRequest = async () => {\n    setLocationLoading(true);\n    setLocationError('');\n    try {\n      // First check permission state\n      const permissionState = await checkLocationPermission();\n      setLocationPermission(permissionState);\n\n      // If permission is denied, provide user guidance\n      if (permissionState === 'denied') {\n        const errorMsg = 'Location access denied. To enable location access:\\n' + '• Safari: Settings > Privacy & Security > Location Services\\n' + '• Chrome: Settings > Privacy > Location\\n' + '• Firefox: Settings > Privacy > Location\\n' + 'Then refresh this page and try again.';\n        setLocationError(errorMsg);\n        setCoordinates('Permission Denied');\n        return;\n      }\n\n      // Request location\n      const position = await requestLocation();\n      const {\n        latitude,\n        longitude\n      } = position.coords;\n\n      // Format coordinates with better precision\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\n      setCoordinates(formattedCoords);\n      setLocationPermission('granted');\n      setLocationError('');\n      console.log('Location acquired:', {\n        latitude,\n        longitude,\n        accuracy: position.coords.accuracy\n      });\n    } catch (error) {\n      console.error('Location request failed:', error);\n      setLocationError(error.message);\n      setCoordinates('Location Error');\n\n      // Update permission state based on error\n      if (error.message.includes('denied')) {\n        setLocationPermission('denied');\n      }\n    } finally {\n      setLocationLoading(false);\n    }\n  };\n\n  // Handle multiple file input change\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      setImageFiles([...imageFiles, ...files]);\n\n      // Create previews and location data for each file\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreviewsMap(prev => ({\n            ...prev,\n            [file.name]: reader.result\n          }));\n        };\n        reader.readAsDataURL(file);\n\n        // Store location as \"Not Available\" for uploaded files\n        setImageLocationMap(prev => ({\n          ...prev,\n          [file.name]: 'Not Available'\n        }));\n      });\n\n      // Reset results\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n    }\n  };\n\n  // Handle camera capture with location validation\n  const handleCapture = async () => {\n    const imageSrc = webcamRef.current.getScreenshot();\n    if (imageSrc) {\n      // If we don't have location data, try to get it before capturing\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\n        await handleLocationRequest();\n      }\n      const timestamp = new Date().toISOString();\n      const filename = `camera_capture_${timestamp}.jpg`;\n      const captureCoordinates = coordinates; // Capture current coordinates\n\n      setImageFiles([...imageFiles, filename]);\n      setImagePreviewsMap(prev => ({\n        ...prev,\n        [filename]: imageSrc\n      }));\n      setImageLocationMap(prev => ({\n        ...prev,\n        [filename]: captureCoordinates\n      }));\n      setCurrentImageIndex(imageFiles.length);\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n\n      // Log capture with current coordinates\n      console.log('Photo captured with coordinates:', captureCoordinates);\n    }\n  };\n\n  // Get location data for currently selected image\n  const getCurrentImageLocation = () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      return coordinates; // Use current coordinates if no images\n    }\n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n    return imageLocationMap[currentFilename] || 'Not Available';\n  };\n\n  // Toggle camera with improved location handling\n  const toggleCamera = async () => {\n    const newCameraState = !cameraActive;\n    setCameraActive(newCameraState);\n    if (newCameraState) {\n      // Get location when camera is activated\n      await handleLocationRequest();\n    } else {\n      // Only reset location if no images are captured\n      // This preserves location data for captured images\n      if (Object.keys(imagePreviewsMap).length === 0) {\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n      }\n    }\n  };\n\n  // Toggle camera orientation (front/back) for mobile devices\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Helper function to handle classification errors\n  const handleClassificationError = errorMessage => {\n    setClassificationError(errorMessage);\n    setShowClassificationModal(true);\n    setError(''); // Clear general error since we're showing specific modal\n  };\n\n  // Process image for detection\n  const handleProcess = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      // Get user info from session storage\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n\n      // Get the currently selected image\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\n      if (!currentImagePreview) {\n        setError('No image selected for processing');\n        setLoading(false);\n        return;\n      }\n\n      // Get coordinates for the current image\n      const imageCoordinates = getCurrentImageLocation();\n\n      // Get the current image filename\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Prepare request data\n      const requestData = {\n        image: currentImagePreview,\n        coordinates: imageCoordinates,\n        username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n        role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown',\n        skip_road_classification: !roadClassificationEnabled\n      };\n\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n\n      // Make API request\n      const response = await axios.post(endpoint, requestData);\n\n      // Handle response\n      if (response.data.success) {\n        var _response$data$classi;\n        // Check if the image was actually processed (contains road) or just classified\n        const isProcessed = response.data.processed !== false;\n        const isRoad = ((_response$data$classi = response.data.classification) === null || _response$data$classi === void 0 ? void 0 : _response$data$classi.is_road) || false;\n\n        // Set the processed image and results for display\n        setProcessedImage(response.data.processed_image);\n        setResults(response.data);\n\n        // Extract detailed detection results for table display\n        const detectionResults = {\n          potholes: response.data.potholes || [],\n          cracks: response.data.cracks || [],\n          kerbs: response.data.kerbs || []\n        };\n\n        // Create batch result entry for the status table\n        const batchResult = {\n          filename: currentFilename,\n          success: true,\n          processed: isProcessed,\n          isRoad: isRoad,\n          classification: response.data.classification,\n          processedImage: response.data.processed_image,\n          data: response.data,\n          detectionResults: detectionResults,\n          detectionCounts: {\n            potholes: detectionResults.potholes.length,\n            cracks: detectionResults.cracks.length,\n            kerbs: detectionResults.kerbs.length,\n            total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n          }\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        // Auto-clear uploaded image icons after successful single image processing\n        // Store the processed image data before clearing (for both road and non-road)\n        setProcessedImagesData(prev => ({\n          ...prev,\n          [currentFilename]: {\n            originalImage: currentImagePreview,\n            processedImage: isRoad ? response.data.processed_image : null,\n            results: response.data,\n            isRoad: isRoad\n          }\n        }));\n\n        // Clear image previews and files but keep results\n        setImageFiles([]);\n        setImagePreviewsMap({});\n        setImageLocationMap({});\n        setCurrentImageIndex(0);\n\n        // Reset coordinates when clearing all images\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n        if (fileInputRef.current) {\n          fileInputRef.current.value = '';\n        }\n      } else {\n        const errorMessage = response.data.message || 'Detection failed';\n\n        // Create batch result entry for failed processing\n        const batchResult = {\n          filename: currentFilename,\n          success: false,\n          processed: false,\n          isRoad: false,\n          error: errorMessage,\n          isClassificationError: errorMessage.includes('No road detected')\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n        setError(errorMessage);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'An error occurred during detection. Please try again.';\n\n      // Get the current image filename for batch results\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Create batch result entry for error case\n      const batchResult = {\n        filename: currentFilename,\n        success: false,\n        processed: false,\n        isRoad: false,\n        error: errorMessage,\n        isClassificationError: errorMessage.includes('No road detected')\n      };\n\n      // Update batch results to show the status table\n      setBatchResults([batchResult]);\n\n      // Check if this is a classification error (no road detected)\n      if (errorMessage.includes('No road detected')) {\n        handleClassificationError(errorMessage);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add a new function to process all images\n  const handleProcessAll = async () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      setError('No images to process');\n      return;\n    }\n    setBatchProcessing(true);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\n\n    // Get user info from session storage\n    const userString = sessionStorage.getItem('user');\n    const user = userString ? JSON.parse(userString) : null;\n    try {\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n      const results = [];\n      const filenames = Object.keys(imagePreviewsMap);\n\n      // Process each image sequentially and display immediately\n      for (let i = 0; i < filenames.length; i++) {\n        const filename = filenames[i];\n        const imageData = imagePreviewsMap[filename];\n        try {\n          // Update current image index to show which image is being processed\n          setCurrentImageIndex(i);\n\n          // Get coordinates for this specific image\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\n\n          // Prepare request data\n          const requestData = {\n            image: imageData,\n            coordinates: imageCoordinates,\n            username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n            role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown',\n            skip_road_classification: !roadClassificationEnabled\n          };\n\n          // Make API request\n          const response = await axios.post(endpoint, requestData);\n          if (response.data.success) {\n            var _response$data$classi2;\n            // Check if the image was actually processed (contains road) or just classified\n            const isProcessed = response.data.processed !== false;\n            const isRoad = ((_response$data$classi2 = response.data.classification) === null || _response$data$classi2 === void 0 ? void 0 : _response$data$classi2.is_road) || false;\n            if (isProcessed && isRoad) {\n              // Road image that was processed - display the results\n              setProcessedImage(response.data.processed_image);\n              setResults(response.data);\n            }\n\n            // Extract detailed detection results for table display\n            const detectionResults = {\n              potholes: response.data.potholes || [],\n              cracks: response.data.cracks || [],\n              kerbs: response.data.kerbs || []\n            };\n            results.push({\n              filename,\n              success: true,\n              processed: isProcessed,\n              isRoad: isRoad,\n              classification: response.data.classification,\n              processedImage: response.data.processed_image,\n              data: response.data,\n              detectionResults: detectionResults,\n              detectionCounts: {\n                potholes: detectionResults.potholes.length,\n                cracks: detectionResults.cracks.length,\n                kerbs: detectionResults.kerbs.length,\n                total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n              }\n            });\n          } else {\n            const errorMessage = response.data.message || 'Detection failed';\n            results.push({\n              filename,\n              success: false,\n              processed: false,\n              isRoad: false,\n              error: errorMessage,\n              isClassificationError: errorMessage.includes('No road detected')\n            });\n          }\n        } catch (error) {\n          var _error$response2, _error$response2$data;\n          const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred during detection';\n          results.push({\n            filename,\n            success: false,\n            processed: false,\n            isRoad: false,\n            error: errorMessage,\n            isClassificationError: errorMessage.includes('No road detected')\n          });\n        }\n\n        // Update progress\n        setProcessedCount(prev => prev + 1);\n\n        // Pause briefly to allow user to see the result before moving to next image\n        // Only pause if not on the last image\n        if (i < filenames.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\n        }\n      }\n\n      // Store final results\n      setBatchResults(results);\n\n      // After batch processing is complete, display the first successfully processed road image\n      const processedRoadImages = results.filter(r => r.success && r.processed && r.isRoad);\n      if (processedRoadImages.length > 0) {\n        const firstProcessedRoadImage = processedRoadImages[0];\n        setProcessedImage(firstProcessedRoadImage.processedImage);\n        setResults(firstProcessedRoadImage.data);\n\n        // Set the current image index to 0 (first processed road image)\n        setCurrentImageIndex(0);\n      } else {\n        // No road images were processed, clear the display\n        setProcessedImage(null);\n        setResults(null);\n        setCurrentImageIndex(0);\n      }\n\n      // Auto-clear uploaded image icons after processing is complete\n      // Store processed images data before clearing (for both road and non-road)\n      const processedData = {};\n      results.forEach(result => {\n        if (result.success) {\n          const originalImage = imagePreviewsMap[result.filename];\n          processedData[result.filename] = {\n            originalImage: originalImage,\n            processedImage: result.isRoad ? result.processedImage : null,\n            results: result.data,\n            isRoad: result.isRoad\n          };\n          console.log('Storing image data for:', result.filename, 'isRoad:', result.isRoad, 'hasOriginalImage:', !!originalImage);\n        }\n      });\n      setProcessedImagesData(prev => ({\n        ...prev,\n        ...processedData\n      }));\n\n      // Clear image previews and files but keep results\n      setImageFiles([]);\n      setImagePreviewsMap({});\n      setImageLocationMap({});\n      setCurrentImageIndex(0);\n\n      // Reset coordinates when clearing all images\n      setCoordinates('Not Available');\n      setLocationError('');\n      setLocationPermission('unknown');\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error) {\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\n    } finally {\n      setBatchProcessing(false);\n    }\n  };\n\n  // Reset detection\n  const handleReset = () => {\n    setImageFiles([]);\n    setImagePreviewsMap({});\n    setImageLocationMap({});\n    setCurrentImageIndex(0);\n    setProcessedImage(null);\n    setResults(null);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(0);\n    setProcessedImagesData({});\n\n    // Reset coordinates when clearing all images\n    setCoordinates('Not Available');\n    setLocationError('');\n    setLocationPermission('unknown');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Helper function to get processed road images\n  const getProcessedRoadImages = () => {\n    return batchResults.filter(r => r.success && r.processed && r.isRoad);\n  };\n\n  // Helper function to get current processed image index\n  const getCurrentProcessedImageIndex = () => {\n    const processedImages = getProcessedRoadImages();\n    // If we have processed images and a current image index, use it directly\n    if (processedImages.length > 0 && currentImageIndex < processedImages.length) {\n      return currentImageIndex;\n    }\n    // Fallback: try to find by filename if imagePreviewsMap still has data\n    if (Object.keys(imagePreviewsMap).length > 0) {\n      const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n      return processedImages.findIndex(r => r.filename === currentFilename);\n    }\n    return 0; // Default to first image if no match found\n  };\n\n  // Add function to handle thumbnail clicks\n  const handleThumbnailClick = imageData => {\n    setSelectedImageData(imageData);\n    setShowImageModal(true);\n  };\n\n  // Add sorting function for detection results\n  const handleSort = key => {\n    let direction = 'asc';\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n    setSortConfig({\n      key,\n      direction\n    });\n  };\n\n  // Function to sort detection results\n  const sortDetections = detections => {\n    if (!sortConfig.key) return detections;\n    return [...detections].sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n\n      // Handle numeric values\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n\n      // Handle string values\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n      }\n\n      // Handle null/undefined values\n      if (aValue == null && bValue == null) return 0;\n      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\n      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\n      return 0;\n    });\n  };\n\n  // Function to export detection results to CSV\n  const exportToCSV = () => {\n    // Flatten all detection results\n    const allDetections = [];\n    batchResults.forEach(result => {\n      if (result.success && result.processed && result.detectionResults) {\n        const {\n          potholes,\n          cracks,\n          kerbs\n        } = result.detectionResults;\n\n        // Add potholes\n        potholes.forEach(pothole => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Pothole',\n            id: pothole.pothole_id,\n            area_cm2: pothole.area_cm2,\n            depth_cm: pothole.depth_cm,\n            volume: pothole.volume,\n            volume_range: pothole.volume_range,\n            crack_type: '',\n            area_range: '',\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: pothole.confidence\n          });\n        });\n\n        // Add cracks\n        cracks.forEach(crack => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Crack',\n            id: crack.crack_id,\n            area_cm2: crack.area_cm2,\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: crack.crack_type,\n            area_range: crack.area_range,\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: crack.confidence\n          });\n        });\n\n        // Add kerbs\n        kerbs.forEach(kerb => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Kerb',\n            id: kerb.kerb_id,\n            area_cm2: '',\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: '',\n            area_range: '',\n            kerb_type: kerb.kerb_type,\n            condition: kerb.condition,\n            length_m: kerb.length_m,\n            confidence: kerb.confidence\n          });\n        });\n      }\n    });\n    if (allDetections.length === 0) {\n      alert('No detection results to export.');\n      return;\n    }\n\n    // Create CSV content\n    const headers = ['Image Filename', 'Detection Type', 'ID', 'Area (cm²)', 'Depth (cm)', 'Volume (cm³)', 'Volume Range', 'Crack Type', 'Area Range', 'Kerb Type', 'Condition', 'Length (m)', 'Confidence'];\n    const csvContent = [headers.join(','), ...allDetections.map(detection => [detection.filename, detection.type, detection.id || '', detection.area_cm2 || '', detection.depth_cm || '', detection.volume || '', detection.volume_range || '', detection.crack_type || '', detection.area_range || '', detection.kerb_type || '', detection.condition || '', detection.length_m || '', detection.confidence || ''].join(','))].join('\\n');\n\n    // Create and download file\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // Add a function to handle auto-navigation through results\n  const startAutoNavigation = () => {\n    if (batchResults.length === 0) return;\n\n    // Find only successful results\n    const successfulResults = batchResults.filter(result => result.success);\n    if (successfulResults.length === 0) return;\n    setAutoNavigationActive(true);\n    setAutoNavigationIndex(0);\n\n    // Display the first result\n    const firstResult = successfulResults[0];\n    setCurrentImageIndex(0);\n    setProcessedImage(firstResult.processedImage);\n    setResults(firstResult.data);\n\n    // Set up interval for auto-navigation\n    autoNavigationRef.current = setInterval(() => {\n      setAutoNavigationIndex(prevIndex => {\n        const nextIndex = prevIndex + 1;\n\n        // If we've reached the end, stop auto-navigation\n        if (nextIndex >= successfulResults.length) {\n          clearInterval(autoNavigationRef.current);\n          setAutoNavigationActive(false);\n          return prevIndex;\n        }\n\n        // Display the next result\n        const nextResult = successfulResults[nextIndex];\n        setCurrentImageIndex(nextIndex);\n        setProcessedImage(nextResult.processedImage);\n        setResults(nextResult.data);\n        return nextIndex;\n      });\n    }, 3000); // Change results every 3 seconds\n  };\n\n  // Clean up interval on component unmount\n  useEffect(() => {\n    return () => {\n      if (autoNavigationRef.current) {\n        clearInterval(autoNavigationRef.current);\n      }\n    };\n  }, []);\n\n  // Handle location permission changes\n  useEffect(() => {\n    if (cameraActive && locationPermission === 'unknown') {\n      // Try to get location when camera is first activated\n      handleLocationRequest();\n    }\n  }, [cameraActive]);\n\n  // Listen for permission changes if supported\n  useEffect(() => {\n    let permissionWatcher = null;\n    const watchPermissions = async () => {\n      try {\n        if (navigator.permissions && navigator.permissions.query) {\n          const permission = await navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permissionWatcher = () => {\n            setLocationPermission(permission.state);\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\n              handleLocationRequest();\n            }\n          };\n          permission.addEventListener('change', permissionWatcher);\n        }\n      } catch (err) {\n        console.warn('Permission watching not supported:', err);\n      }\n    };\n    watchPermissions();\n    return () => {\n      if (permissionWatcher) {\n        try {\n          const permission = navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\n        } catch (err) {\n          console.warn('Error removing permission listener:', err);\n        }\n      }\n    };\n  }, [cameraActive, coordinates]);\n\n  // Force re-render when current image changes to update location display\n  useEffect(() => {\n    // This effect ensures the UI updates when switching between images\n    // The getCurrentImageLocation function will return the correct location for the selected image\n  }, [currentImageIndex, imageLocationMap]);\n\n  // Stop auto-navigation\n  const stopAutoNavigation = () => {\n    if (autoNavigationRef.current) {\n      clearInterval(autoNavigationRef.current);\n      setAutoNavigationActive(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"pavement-page\",\n    children: [/*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: k => setActiveTab(k),\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"detection\",\n        title: \"Image Detection\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"py-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"mb-1\",\n                children: \"Detection Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: detectionType,\n                onChange: e => setDetectionType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All (Potholes + Cracks + Kerbs)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"potholes\",\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cracks\",\n                  children: \"Alligator Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"kerbs\",\n                  children: \"Kerbs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-start gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                trigger: \"click\",\n                placement: \"right\",\n                overlay: reminderPopover,\n                rootClose: true,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sticky-note-icon\",\n                  style: {\n                    cursor: 'pointer',\n                    display: 'inline-block'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/remindericon.svg\",\n                    alt: \"Image Upload Guidelines\",\n                    style: {\n                      width: '28px',\n                      height: '28px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"road-classification-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center justify-content-between mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"me-2\",\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: '500',\n                      color: '#495057'\n                    },\n                    children: \"Road Classification\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                    placement: \"right\",\n                    delay: {\n                      show: 200,\n                      hide: 100\n                    },\n                    overlay: /*#__PURE__*/_jsxDEV(Popover, {\n                      id: \"road-classification-detailed-info\",\n                      style: {\n                        maxWidth: '350px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Popover.Header, {\n                        as: \"h3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-brain me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1036,\n                          columnNumber: 31\n                        }, this), \"Road Classification Feature\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Popover.Body, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-toggle-on text-success me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1042,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"ENABLED (ON):\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1043,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1041,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: '12px',\n                              color: '#6c757d',\n                              marginLeft: '20px'\n                            },\n                            children: [\"\\u2022 AI analyzes images for road content first\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1046,\n                              columnNumber: 78\n                            }, this), \"\\u2022 Only road images get defect detection\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1047,\n                              columnNumber: 74\n                            }, this), \"\\u2022 More accurate results, slightly slower\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1045,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1040,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                              className: \"fas fa-toggle-off text-secondary me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1054,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"DISABLED (OFF):\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1055,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1053,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: '12px',\n                              color: '#6c757d',\n                              marginLeft: '20px'\n                            },\n                            children: [\"\\u2022 All images processed directly\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1058,\n                              columnNumber: 66\n                            }, this), \"\\u2022 No road verification step\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1059,\n                              columnNumber: 62\n                            }, this), \"\\u2022 Faster processing, may have false positives\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1057,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1052,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"alert alert-info py-2 px-2 mb-0\",\n                          style: {\n                            fontSize: '11px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-lightbulb me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1065,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Recommendation:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1066,\n                            columnNumber: 33\n                          }, this), \" Keep enabled for mixed image types. Disable only when all images contain roads and speed is priority.\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1064,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1039,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 27\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"info-icon-wrapper\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"road-classification-info-icon\",\n                        style: {\n                          fontSize: '14px',\n                          cursor: 'help',\n                          color: '#007bff',\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          position: 'relative',\n                          zIndex: '1000',\n                          fontWeight: 'bold'\n                        },\n                        children: \"i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1074,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"toggle-switch me-2\",\n                    onClick: () => setRoadClassificationEnabled(!roadClassificationEnabled),\n                    style: {\n                      width: '60px',\n                      height: '30px',\n                      backgroundColor: roadClassificationEnabled ? '#28a745' : '#6c757d',\n                      borderRadius: '15px',\n                      position: 'relative',\n                      cursor: 'pointer',\n                      transition: 'background-color 0.3s ease',\n                      border: '2px solid transparent'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"toggle-slider\",\n                      style: {\n                        width: '22px',\n                        height: '22px',\n                        backgroundColor: 'white',\n                        borderRadius: '50%',\n                        position: 'absolute',\n                        top: '2px',\n                        left: roadClassificationEnabled ? '34px' : '2px',\n                        transition: 'left 0.3s ease',\n                        boxShadow: '0 2px 4px rgba(0,0,0,0.2)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1105,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        position: 'absolute',\n                        top: '50%',\n                        left: roadClassificationEnabled ? '8px' : '32px',\n                        transform: 'translateY(-50%)',\n                        fontSize: '10px',\n                        fontWeight: '600',\n                        color: 'white',\n                        transition: 'all 0.3s ease',\n                        userSelect: 'none'\n                      },\n                      children: roadClassificationEnabled ? 'ON' : 'OFF'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    style: {\n                      fontSize: '11px'\n                    },\n                    children: roadClassificationEnabled ? \"Only road images processed\" : \"All images processed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Image Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: cameraActive ? \"primary\" : \"outline-primary\",\n                  onClick: toggleCamera,\n                  disabled: locationLoading,\n                  children: locationLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      as: \"span\",\n                      animation: \"border\",\n                      size: \"sm\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1154,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ms-2\",\n                      children: \"Getting Location...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : cameraActive ? \"Disable Camera\" : \"Enable Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-input-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"file-input-label\",\n                    children: [\"Upload Image\", /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      className: \"file-input\",\n                      accept: \"image/*\",\n                      onChange: handleFileChange,\n                      ref: fileInputRef,\n                      disabled: cameraActive,\n                      multiple: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1164,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1162,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"location-status mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Location Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 23\n                  }, this), locationPermission === 'granted' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success ms-1\",\n                    children: \"\\u2713 Enabled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1182,\n                    columnNumber: 60\n                  }, this), locationPermission === 'denied' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger ms-1\",\n                    children: \"\\u2717 Denied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1183,\n                    columnNumber: 59\n                  }, this), locationPermission === 'prompt' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-warning ms-1\",\n                    children: \"\\u26A0 Requesting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1184,\n                    columnNumber: 59\n                  }, this), locationPermission === 'unknown' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-secondary ms-1\",\n                    children: \"? Unknown\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1185,\n                    columnNumber: 60\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 21\n                }, this), (coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Current Location:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1190,\n                      columnNumber: 27\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary\",\n                      children: coordinates\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1190,\n                      columnNumber: 62\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1189,\n                    columnNumber: 25\n                  }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Selected Image Location:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1195,\n                        columnNumber: 31\n                      }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-primary\",\n                        children: getCurrentImageLocation()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1195,\n                        columnNumber: 73\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 23\n                }, this), locationError && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"warning\",\n                  className: \"mt-2 mb-0\",\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n                    as: \"h6\",\n                    children: \"Location Access Issue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: 'pre-line'\n                    },\n                    children: locationError\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-end\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-warning\",\n                      size: \"sm\",\n                      onClick: handleLocationRequest,\n                      children: \"Retry Location Access\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1144,\n              columnNumber: 15\n            }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                audio: false,\n                ref: webcamRef,\n                screenshotFormat: \"image/jpeg\",\n                className: \"webcam\",\n                videoConstraints: {\n                  width: 640,\n                  height: 480,\n                  facingMode: cameraOrientation\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 19\n              }, this), isMobile && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                onClick: toggleCameraOrientation,\n                className: \"mt-2 mb-2\",\n                size: \"sm\",\n                children: \"Rotate Camera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleCapture,\n                className: \"mt-2\",\n                children: \"Capture Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-preview-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Previews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-gallery\",\n                children: Object.entries(imagePreviewsMap).map(([name, preview], index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`,\n                  onClick: () => setCurrentImageIndex(index),\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Preview ${index + 1}`,\n                    className: \"img-thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-danger remove-image\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      const newFiles = imageFiles.filter((_, i) => i !== index);\n                      const newPreviewsMap = {\n                        ...imagePreviewsMap\n                      };\n                      const newLocationMap = {\n                        ...imageLocationMap\n                      };\n                      delete newPreviewsMap[name];\n                      delete newLocationMap[name];\n                      setImageFiles(newFiles);\n                      setImagePreviewsMap(newPreviewsMap);\n                      setImageLocationMap(newLocationMap);\n                      if (currentImageIndex >= newFiles.length) {\n                        setCurrentImageIndex(Math.max(0, newFiles.length - 1));\n                      }\n                    },\n                    children: \"\\xD7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 25\n                  }, this)]\n                }, name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-image-preview\",\n                children: Object.values(imagePreviewsMap)[currentImageIndex] && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Object.values(imagePreviewsMap)[currentImageIndex],\n                  alt: \"Current Preview\",\n                  className: \"image-preview img-fluid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1251,\n              columnNumber: 17\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1299,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: handleProcess,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1309,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: \"Detecting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1310,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Detect Current Image`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleProcessAll,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: batchProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1324,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: [\"Processing \", processedCount, \"/\", totalToProcess]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1325,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Process All Images`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleReset,\n                disabled: loading || batchProcessing,\n                children: \"Reset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this), batchResults.some(result => {\n          var _result$detectionCoun;\n          return result.success && result.processed && ((_result$detectionCoun = result.detectionCounts) === null || _result$detectionCoun === void 0 ? void 0 : _result$detectionCoun.total) > 0;\n        }) && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-table me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 21\n                }, this), \"Detailed Detection Results\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: showDetailedResults ? 'primary' : 'outline-primary',\n                  size: \"sm\",\n                  onClick: () => setShowDetailedResults(!showDetailedResults),\n                  children: showDetailedResults ? 'Hide Details' : 'Show Details'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"success\",\n                  size: \"sm\",\n                  onClick: exportToCSV,\n                  title: \"Export results to CSV\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-download me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1367,\n                    columnNumber: 23\n                  }, this), \"Export CSV\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1348,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1347,\n            columnNumber: 15\n          }, this), showDetailedResults && /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 flex-wrap\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'all' ? 'primary' : 'outline-primary',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('all'),\n                  children: \"All Detections\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'potholes' ? 'danger' : 'outline-danger',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('potholes'),\n                  children: \"Potholes Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'cracks' ? 'warning' : 'outline-warning',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('cracks'),\n                  children: \"Cracks Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1392,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: detectionTableFilter === 'kerbs' ? 'info' : 'outline-info',\n                  size: \"sm\",\n                  onClick: () => setDetectionTableFilter('kerbs'),\n                  children: \"Kerbs Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1399,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 detection-summary-cards\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-danger text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Potholes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1415,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun2;\n                          return sum + (((_result$detectionCoun2 = result.detectionCounts) === null || _result$detectionCoun2 === void 0 ? void 0 : _result$detectionCoun2.potholes) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1416,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1414,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1413,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1412,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-warning text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Cracks\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1425,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun3;\n                          return sum + (((_result$detectionCoun3 = result.detectionCounts) === null || _result$detectionCoun3 === void 0 ? void 0 : _result$detectionCoun3.cracks) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1426,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1424,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1423,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-info text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Kerbs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1435,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun4;\n                          return sum + (((_result$detectionCoun4 = result.detectionCounts) === null || _result$detectionCoun4 === void 0 ? void 0 : _result$detectionCoun4.kerbs) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1434,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1433,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1432,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card bg-success text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body text-center py-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-1\",\n                        children: \"Total Detections\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1445,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"mb-0\",\n                        children: batchResults.reduce((sum, result) => {\n                          var _result$detectionCoun5;\n                          return sum + (((_result$detectionCoun5 = result.detectionCounts) === null || _result$detectionCoun5 === void 0 ? void 0 : _result$detectionCoun5.total) || 0);\n                        }, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1446,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1444,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1411,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1410,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive detection-table-container\",\n              children: (() => {\n                // Flatten all detection results into a single array\n                const allDetections = [];\n                batchResults.forEach(result => {\n                  if (result.success && result.processed && result.detectionResults) {\n                    const {\n                      potholes,\n                      cracks,\n                      kerbs\n                    } = result.detectionResults;\n\n                    // Add potholes\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') {\n                      potholes.forEach(pothole => {\n                        allDetections.push({\n                          ...pothole,\n                          type: 'Pothole',\n                          filename: result.filename,\n                          detectionType: 'potholes'\n                        });\n                      });\n                    }\n\n                    // Add cracks\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') {\n                      cracks.forEach(crack => {\n                        allDetections.push({\n                          ...crack,\n                          type: 'Crack',\n                          filename: result.filename,\n                          detectionType: 'cracks'\n                        });\n                      });\n                    }\n\n                    // Add kerbs\n                    if (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') {\n                      kerbs.forEach(kerb => {\n                        allDetections.push({\n                          ...kerb,\n                          type: 'Kerb',\n                          filename: result.filename,\n                          detectionType: 'kerbs'\n                        });\n                      });\n                    }\n                  }\n                });\n                if (allDetections.length === 0) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted\",\n                      children: \"No detections found for the selected filter.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1506,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1505,\n                    columnNumber: 27\n                  }, this);\n                }\n                return /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-striped table-bordered\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Original Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1515,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Processed Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1516,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        onClick: () => handleSort('detectionType'),\n                        children: [\"Type \", sortConfig.key === 'detectionType' && /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1522,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1517,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 31\n                      }, this), (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('area_cm2'),\n                          children: [\"Area (cm\\xB2) \", sortConfig.key === 'area_cm2' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1533,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1528,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('depth_cm'),\n                          children: [\"Depth (cm) \", sortConfig.key === 'depth_cm' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1541,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1536,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('volume'),\n                          children: [\"Volume (cm\\xB3) \", sortConfig.key === 'volume' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1549,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1544,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Volume Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1552,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('crack_type'),\n                          children: [\"Crack Type \", sortConfig.key === 'crack_type' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1562,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1557,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('area_cm2'),\n                          children: [\"Area (cm\\xB2) \", sortConfig.key === 'area_cm2' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1565,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Area Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1573,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('kerb_type'),\n                          children: [\"Kerb Type \", sortConfig.key === 'kerb_type' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1583,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1578,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('condition'),\n                          children: [\"Condition \", sortConfig.key === 'condition' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1591,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1586,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          onClick: () => handleSort('length_m'),\n                          children: [\"Length (m) \", sortConfig.key === 'length_m' && /*#__PURE__*/_jsxDEV(\"i\", {\n                            className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1599,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1594,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true), /*#__PURE__*/_jsxDEV(\"th\", {\n                        style: {\n                          cursor: 'pointer'\n                        },\n                        onClick: () => handleSort('confidence'),\n                        children: [\"Confidence \", sortConfig.key === 'confidence' && /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: `fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1609,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1604,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1514,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: sortDetections(allDetections).map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        style: {\n                          width: '120px',\n                          textAlign: 'center'\n                        },\n                        children: (() => {\n                          const originalImage = imagePreviewsMap[detection.filename];\n                          return originalImage ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"image-thumbnail-container\",\n                            onClick: () => {\n                              var _batchResults$find, _batchResults$find2;\n                              return handleThumbnailClick({\n                                originalImage: originalImage,\n                                processedImage: (_batchResults$find = batchResults.find(r => r.filename === detection.filename)) === null || _batchResults$find === void 0 ? void 0 : _batchResults$find.processedImage,\n                                isRoad: (_batchResults$find2 = batchResults.find(r => r.filename === detection.filename)) === null || _batchResults$find2 === void 0 ? void 0 : _batchResults$find2.isRoad,\n                                filename: detection.filename\n                              });\n                            },\n                            title: \"Click to enlarge \\uD83D\\uDD0D\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: originalImage,\n                              alt: \"Original\",\n                              className: \"image-thumbnail\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1632,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"thumbnail-overlay\",\n                              children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1637,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1622,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted\",\n                            children: \"No image\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1642,\n                            columnNumber: 39\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1618,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        style: {\n                          width: '120px',\n                          textAlign: 'center'\n                        },\n                        children: (() => {\n                          const result = batchResults.find(r => r.filename === detection.filename);\n                          const processedImage = result === null || result === void 0 ? void 0 : result.processedImage;\n                          const originalImage = imagePreviewsMap[detection.filename];\n                          return processedImage && result !== null && result !== void 0 && result.isRoad ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"image-thumbnail-container\",\n                            onClick: () => handleThumbnailClick({\n                              originalImage: originalImage,\n                              processedImage: processedImage,\n                              isRoad: result.isRoad,\n                              filename: detection.filename\n                            }),\n                            title: \"Click to enlarge \\uD83D\\uDD0D\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: processedImage,\n                              alt: \"Processed\",\n                              className: \"image-thumbnail\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1665,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"thumbnail-overlay\",\n                              children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1670,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1655,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted\",\n                            children: \"No processed image\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1675,\n                            columnNumber: 39\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1648,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `badge ${detection.detectionType === 'potholes' ? 'bg-danger' : detection.detectionType === 'cracks' ? 'bg-warning' : 'bg-info'}`,\n                          children: detection.type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1680,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1679,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: detection.pothole_id || detection.crack_id || detection.kerb_id || index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1687,\n                        columnNumber: 33\n                      }, this), (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1692,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1693,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.volume ? detection.volume.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1694,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.volume_range || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1695,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.crack_type || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1702,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1703,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.area_range || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1704,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.kerb_type || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1711,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.condition || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1712,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: detection.length_m ? detection.length_m.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1713,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: detection.confidence ? (detection.confidence * 100).toFixed(1) + '%' : 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1717,\n                        columnNumber: 33\n                      }, this)]\n                    }, `${detection.filename}-${detection.detectionType}-${index}`, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1616,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1614,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1512,\n                  columnNumber: 25\n                }, this);\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1374,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 13\n        }, this), batchProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"batch-processing-status mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"me-3\",\n              children: /*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\",\n                role: \"status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1735,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1734,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-1\",\n                children: [\"Processing images: \", processedCount, \"/\", totalToProcess]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress\",\n                style: {\n                  height: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  role: \"progressbar\",\n                  style: {\n                    width: `${processedCount / totalToProcess * 100}%`\n                  },\n                  \"aria-valuenow\": processedCount,\n                  \"aria-valuemin\": \"0\",\n                  \"aria-valuemax\": totalToProcess\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1740,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1739,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1737,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1733,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1732,\n          columnNumber: 13\n        }, this), !batchProcessing && batchResults.length > 0 && (() => {\n          const totalImages = batchResults.length;\n          const successfulImages = batchResults.filter(r => r.success).length;\n          const failedImages = batchResults.filter(r => !r.success).length;\n          let alertVariant = 'light';\n          let alertClass = '';\n          if (roadClassificationEnabled) {\n            // When classification is enabled, use road/non-road logic\n            const nonRoadImages = batchResults.filter(r => !r.isRoad).length;\n            const nonRoadPercentage = totalImages > 0 ? nonRoadImages / totalImages * 100 : 0;\n            if (totalImages > 0) {\n              if (nonRoadPercentage === 0) {\n                // 100% road detection - Green\n                alertVariant = 'success';\n              } else if (nonRoadPercentage === 100) {\n                // 100% non-road detection - Red\n                alertVariant = 'danger';\n              } else {\n                // Combined detection (mixed results) - Light Orange\n                alertVariant = 'warning';\n                alertClass = 'summary-light-orange';\n              }\n            }\n          } else {\n            // When classification is disabled, use success/failure logic\n            if (failedImages === 0) {\n              // All successful - Green\n              alertVariant = 'success';\n            } else if (successfulImages === 0) {\n              // All failed - Red\n              alertVariant = 'danger';\n            } else {\n              // Mixed results - Warning\n              alertVariant = 'warning';\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"batch-complete-status mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              variant: alertVariant,\n              className: alertClass,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1802,\n                columnNumber: 19\n              }, this), \"Processed \", batchResults.length, \" images.\", roadClassificationEnabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [batchResults.filter(r => r.success && r.processed).length, \" road images processed,\", batchResults.filter(r => r.success && !r.processed).length, \" non-road images detected,\", batchResults.filter(r => !r.success).length, \" failed.\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [batchResults.filter(r => r.success).length, \" images processed successfully,\", batchResults.filter(r => !r.success).length, \" failed.\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1801,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1800,\n            columnNumber: 15\n          }, this);\n        })(), !batchProcessing && batchResults.length > 0 && roadClassificationEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-status-table mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-0\",\n                  children: \"Image Processing Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1827,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'all' ? 'primary' : 'outline-primary',\n                    size: \"sm\",\n                    className: \"me-2\",\n                    onClick: () => setImageFilter('all'),\n                    children: \"Show All Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1829,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'road' ? 'success' : 'outline-success',\n                    size: \"sm\",\n                    className: \"me-2\",\n                    onClick: () => setImageFilter('road'),\n                    children: \"Show Only Road Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: imageFilter === 'non-road' ? 'danger' : 'outline-danger',\n                    size: \"sm\",\n                    onClick: () => setImageFilter('non-road'),\n                    children: \"Show Only Non-Road Images\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1845,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1828,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1826,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1825,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-responsive\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"table table-striped\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Image\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1860,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        children: \"Detection Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1861,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1859,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1858,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    children: batchResults.filter(result => {\n                      if (imageFilter === 'road') return result.isRoad;\n                      if (imageFilter === 'non-road') return !result.isRoad;\n                      return true; // 'all'\n                    }).map((result, index) => {\n                      const filename = result.filename;\n                      const isRoad = result.isRoad;\n\n                      // Get image from stored processed data\n                      let imagePreview = null;\n                      let imageData = null;\n                      if (processedImagesData[filename]) {\n                        // Use stored processed image data\n                        imagePreview = processedImagesData[filename].originalImage;\n                        imageData = processedImagesData[filename];\n                        console.log('Found stored data for:', filename, 'hasImage:', !!imagePreview);\n                      } else if (imagePreviewsMap[filename]) {\n                        // Fallback to current preview (for any remaining unprocessed images)\n                        imagePreview = imagePreviewsMap[filename];\n                        imageData = {\n                          originalImage: imagePreview,\n                          processedImage: null,\n                          results: null,\n                          isRoad: isRoad\n                        };\n                        console.log('Using fallback data for:', filename, 'hasImage:', !!imagePreview);\n                      } else {\n                        console.log('No image data found for:', filename);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center\",\n                            children: [imagePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: imagePreview,\n                              alt: `Thumbnail ${index + 1}`,\n                              className: \"img-thumbnail me-2\",\n                              style: {\n                                width: '60px',\n                                height: '60px',\n                                objectFit: 'cover',\n                                cursor: 'pointer'\n                              },\n                              onClick: () => handleThumbnailClick(imageData),\n                              title: \"Click to view full size\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1903,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"img-thumbnail me-2 d-flex align-items-center justify-content-center\",\n                              style: {\n                                width: '60px',\n                                height: '60px',\n                                backgroundColor: '#f8f9fa',\n                                border: '1px solid #dee2e6'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                                className: \"text-muted\",\n                                children: \"No Image\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1926,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1917,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                              className: \"text-muted\",\n                              children: filename\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1929,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1901,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1900,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `badge ${isRoad ? 'bg-success' : 'bg-danger'}`,\n                            children: isRoad ? 'Road' : 'Non-Road'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1933,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1932,\n                          columnNumber: 33\n                        }, this)]\n                      }, filename, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1899,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1864,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1857,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1856,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1855,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1824,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1823,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 988,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"video\",\n        title: \"Video Detection\",\n        children: /*#__PURE__*/_jsxDEV(VideoDefectDetection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1954,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1953,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"information\",\n        title: \"Information\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"About Pavement Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1960,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The Pavement Analysis module uses advanced computer vision to detect and analyze various types of pavement defects and features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1961,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"1. Potholes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1966,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Potholes are bowl-shaped holes of various sizes in the road surface that can be a serious hazard to vehicles. The system detects potholes and calculates:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1967,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Area in square centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1972,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Depth in centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1973,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Volume\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1974,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Classification by size (Small, Medium, Large)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1975,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1971,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"2. Alligator Cracks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1978,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Alligator cracks are a series of interconnected cracks creating a pattern resembling an alligator's scales. These indicate underlying structural weakness. The system identifies multiple types of cracks including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1979,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Alligator Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1985,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Edge Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1986,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Hairline Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1987,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Longitudinal Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1988,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Transverse Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1984,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"3. Kerbs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1992,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Kerbs are raised edges along a street or path that define boundaries between roadways and other areas. The system identifies different kerb conditions including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1993,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Normal/Good Kerbs - Structurally sound and properly visible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1998,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Faded Kerbs - Reduced visibility due to worn paint or weathering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1999,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Damaged Kerbs - Physically damaged or broken kerbs requiring repair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2000,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1997,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Location Services & GPS Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2003,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"When using the live camera option, the application can capture GPS coordinates to provide precise geolocation data for detected defects. This helps in:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2004,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Accurately mapping defect locations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2009,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Creating location-based reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2010,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Enabling field teams to find specific issues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2011,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Tracking defect patterns by geographic area\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2012,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2008,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Location Requirements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2015,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Secure Connection:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2017,\n                  columnNumber: 21\n                }, this), \" Location services require HTTPS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2017,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Browser Permissions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2018,\n                  columnNumber: 21\n                }, this), \" You must allow location access when prompted\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2018,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Safari Users:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2019,\n                  columnNumber: 21\n                }, this), \" Enable location services in Safari settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2019,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Mobile Devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2020,\n                  columnNumber: 21\n                }, this), \" Ensure location services are enabled in device settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2020,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2016,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-info-circle me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2024,\n                  columnNumber: 21\n                }, this), \"Troubleshooting Location Issues\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2024,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"If location access is denied:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2025,\n                  columnNumber: 20\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2025,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Safari:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2027,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Location Services\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2027,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Chrome:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2028,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy and security \\u2192 Site Settings \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2028,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Firefox:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2029,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Permissions \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2029,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2026,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"On mobile devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2031,\n                  columnNumber: 20\n                }, this), \" Also check your device's location settings and ensure the browser has location permission.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2031,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2023,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"How to Use This Module\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2034,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Select the detection type (Potholes, Alligator Cracks, or Kerbs)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2036,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Upload an image or use the camera to capture a photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2037,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If using the camera, allow location access when prompted for GPS coordinates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2038,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Click the Detect button to analyze the image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2039,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Review the detection results and measurements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2040,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2035,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The detected defects are automatically recorded in the database for tracking and analysis in the Dashboard module.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2043,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1959,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1958,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1957,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 983,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImageModal,\n      onHide: () => setShowImageModal(false),\n      size: \"lg\",\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-image me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2103,\n            columnNumber: 13\n          }, this), \"Image View \", (selectedImageData === null || selectedImageData === void 0 ? void 0 : selectedImageData.filename) && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"- \", selectedImageData.filename]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedImageData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-camera me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2114,\n                columnNumber: 19\n              }, this), \"Original Image\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedImageData.originalImage,\n              alt: \"Original Image\",\n              className: \"img-fluid\",\n              style: {\n                maxHeight: '400px',\n                borderRadius: '8px',\n                border: '2px solid #dee2e6',\n                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2117,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2112,\n            columnNumber: 15\n          }, this), selectedImageData.processedImage && selectedImageData.isRoad && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-search me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2132,\n                columnNumber: 21\n              }, this), \"Processed Image (Detection Results)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2131,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedImageData.processedImage,\n              alt: \"Processed Image\",\n              className: \"img-fluid\",\n              style: {\n                maxHeight: '400px',\n                borderRadius: '8px',\n                border: '2px solid #28a745',\n                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2135,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2130,\n            columnNumber: 17\n          }, this), !selectedImageData.isRoad && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2151,\n                columnNumber: 21\n              }, this), \"This image was classified as non-road and therefore no defect detection was performed.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2150,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2149,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2111,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImageModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2095,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 981,\n    columnNumber: 5\n  }, this);\n};\n\n// Add CSS styles for the enhanced detection table\n_s(Pavement, \"dG8bB7NZj7g20oNaR9tO5DRhlqE=\", false, function () {\n  return [useResponsive];\n});\n_c = Pavement;\nconst styles = `\n  .detection-table-container {\n    max-height: 600px;\n    overflow-y: auto;\n  }\n\n  .detection-table-container th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 10;\n  }\n\n  .detection-table-container th:hover {\n    background-color: #e9ecef;\n  }\n\n  .detection-summary-cards .card {\n    transition: transform 0.2s ease-in-out;\n  }\n\n  .detection-summary-cards .card:hover {\n    transform: translateY(-2px);\n  }\n\n  .table-responsive {\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  }\n\n  .badge {\n    font-size: 0.75em;\n  }\n\n  @media (max-width: 768px) {\n    .detection-summary-cards .col-md-3 {\n      margin-bottom: 1rem;\n    }\n\n    .d-flex.gap-2.flex-wrap {\n      flex-direction: column;\n    }\n\n    .d-flex.gap-2.flex-wrap .btn {\n      margin-bottom: 0.5rem;\n    }\n  }\n`;\n\n// Inject styles into the document head\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.type = 'text/css';\n  styleSheet.innerText = styles;\n  document.head.appendChild(styleSheet);\n}\nexport default Pavement;\nvar _c;\n$RefreshReg$(_c, \"Pavement\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Card", "<PERSON><PERSON>", "Form", "Tabs", "Tab", "<PERSON><PERSON>", "Spinner", "OverlayTrigger", "Popover", "Modal", "axios", "Webcam", "useResponsive", "VideoDefectDetection", "FaArrowLeft", "FaArrowRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Pavement", "_s", "activeTab", "setActiveTab", "detectionType", "setDetectionType", "imageFiles", "setImageFiles", "imagePreviewsMap", "setImagePreviewsMap", "imageLocationMap", "setImageLocationMap", "currentImageIndex", "setCurrentImageIndex", "processedImage", "setProcessedImage", "results", "setResults", "loading", "setLoading", "error", "setError", "cameraActive", "setCameraActive", "coordinates", "setCoordinates", "cameraOrientation", "setCameraOrientation", "locationPermission", "setLocationPermission", "locationError", "setLocationError", "locationLoading", "setLocationLoading", "batchResults", "setBatchResults", "batchProcessing", "setBatchProcessing", "processedCount", "setProcessedCount", "processedImagesData", "setProcessedImagesData", "showClassificationModal", "setShowClassificationModal", "classificationError", "setClassificationError", "totalToProcess", "setTotalToProcess", "showImageModal", "setShowImageModal", "selectedImageData", "setSelectedImageData", "imageFilter", "setImageFilter", "autoNavigationActive", "setAutoNavigationActive", "autoNavigationIndex", "setAutoNavigationIndex", "autoNavigationRef", "roadClassificationEnabled", "setRoadClassificationEnabled", "detectionTableFilter", "setDetectionTableFilter", "showDetailedResults", "setShowDetailedResults", "sortConfig", "setSortConfig", "key", "direction", "webcamRef", "fileInputRef", "isMobile", "reminderPopover", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "Header", "as", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "marginBottom", "paddingLeft", "checkLocationPermission", "navigator", "permissions", "query", "permission", "name", "state", "err", "console", "warn", "requestLocation", "Promise", "resolve", "reject", "geolocation", "Error", "window", "isSecureContext", "options", "enableHighAccuracy", "timeout", "maximumAge", "getCurrentPosition", "position", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "message", "handleLocationRequest", "permissionState", "errorMsg", "latitude", "longitude", "coords", "formattedCoords", "toFixed", "log", "accuracy", "includes", "handleFileChange", "e", "files", "Array", "from", "target", "length", "for<PERSON>ach", "file", "reader", "FileReader", "onloadend", "prev", "result", "readAsDataURL", "handleCapture", "imageSrc", "current", "getScreenshot", "timestamp", "Date", "toISOString", "filename", "captureCoordinates", "getCurrentImageLocation", "Object", "keys", "currentFilename", "toggleCamera", "newCameraState", "toggleCameraOrientation", "handleClassificationError", "handleProcess", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "currentImagePreview", "values", "imageCoordinates", "filenames", "requestData", "image", "username", "role", "skip_road_classification", "endpoint", "response", "post", "data", "success", "_response$data$classi", "isProcessed", "processed", "isRoad", "classification", "is_road", "processed_image", "detectionResults", "potholes", "cracks", "kerbs", "batchResult", "detectionCounts", "total", "originalImage", "value", "isClassificationError", "_error$response", "_error$response$data", "handleProcessAll", "i", "imageData", "_response$data$classi2", "push", "_error$response2", "_error$response2$data", "setTimeout", "processedRoadImages", "filter", "r", "firstProcessedRoadImage", "processedData", "handleReset", "getProcessedRoadImages", "getCurrentProcessedImageIndex", "processedImages", "findIndex", "handleThumbnailClick", "handleSort", "sortDetections", "detections", "sort", "a", "b", "aValue", "bValue", "localeCompare", "exportToCSV", "allDetections", "pothole", "type", "pothole_id", "area_cm2", "depth_cm", "volume", "volume_range", "crack_type", "area_range", "kerb_type", "condition", "length_m", "confidence", "crack", "crack_id", "kerb", "kerb_id", "alert", "headers", "csv<PERSON><PERSON>nt", "join", "map", "detection", "blob", "Blob", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "split", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "startAutoNavigation", "successfulResults", "firstResult", "setInterval", "prevIndex", "nextIndex", "clearInterval", "nextResult", "permissionWatcher", "watchPermissions", "addEventListener", "then", "p", "removeEventListener", "stopAutoNavigation", "className", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "Group", "Label", "Select", "onChange", "trigger", "placement", "overlay", "rootClose", "cursor", "display", "src", "alt", "width", "height", "fontSize", "fontWeight", "color", "delay", "show", "hide", "marginLeft", "alignItems", "justifyContent", "zIndex", "onClick", "backgroundColor", "borderRadius", "transition", "border", "top", "left", "boxShadow", "transform", "userSelect", "variant", "disabled", "animation", "size", "accept", "ref", "multiple", "Heading", "whiteSpace", "audio", "screenshotFormat", "videoConstraints", "facingMode", "entries", "preview", "index", "stopPropagation", "newFiles", "_", "newPreviewsMap", "newLocationMap", "Math", "max", "some", "_result$detectionCoun", "reduce", "sum", "_result$detectionCoun2", "_result$detectionCoun3", "_result$detectionCoun4", "_result$detectionCoun5", "textAlign", "_batchResults$find", "_batchResults$find2", "find", "totalImages", "successfulImages", "failedImages", "alertVariant", "alertClass", "nonRoadImages", "nonRoadPercentage", "imagePreview", "objectFit", "onHide", "centered", "closeButton", "Title", "maxHeight", "Footer", "_c", "styles", "styleSheet", "innerText", "head", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Pavement.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Form, Tabs, Tab, <PERSON><PERSON>, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport './Pavement.css';\nimport useResponsive from '../hooks/useResponsive';\nimport VideoDefectDetection from '../components/VideoDefectDetection';\nimport { FaArrowLeft, FaArrowRight } from 'react-icons/fa';\n\nconst Pavement = () => {\n  const [activeTab, setActiveTab] = useState('detection');\n  const [detectionType, setDetectionType] = useState('all');\n  const [imageFiles, setImageFiles] = useState([]);\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\n  const [imageLocationMap, setImageLocationMap] = useState({});\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [processedImage, setProcessedImage] = useState(null);\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [locationPermission, setLocationPermission] = useState('unknown');\n  const [locationError, setLocationError] = useState('');\n  const [locationLoading, setLocationLoading] = useState(false);\n  \n  // Add state for batch processing results\n  const [batchResults, setBatchResults] = useState([]);\n  const [batchProcessing, setBatchProcessing] = useState(false);\n  const [processedCount, setProcessedCount] = useState(0);\n  \n  // Add state for storing processed images for results table\n  const [processedImagesData, setProcessedImagesData] = useState({});\n\n  // Add state for classification error modal\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\n  const [classificationError, setClassificationError] = useState('');\n  const [totalToProcess, setTotalToProcess] = useState(0);\n  \n  // Add state for image modal\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [selectedImageData, setSelectedImageData] = useState(null);\n\n  // Add state for image status table filtering\n  const [imageFilter, setImageFilter] = useState('all'); // 'all', 'road', 'non-road'\n\n  // Add state for auto-navigation through results\n  const [autoNavigationActive, setAutoNavigationActive] = useState(false);\n  const [autoNavigationIndex, setAutoNavigationIndex] = useState(0);\n  const autoNavigationRef = useRef(null);\n\n  // Add state for road classification toggle (default to false for better user experience)\n  const [roadClassificationEnabled, setRoadClassificationEnabled] = useState(false);\n\n  // Add state for enhanced detection results table\n  const [detectionTableFilter, setDetectionTableFilter] = useState('all'); // 'all', 'potholes', 'cracks', 'kerbs'\n  const [showDetailedResults, setShowDetailedResults] = useState(false);\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });\n\n  // Auto-clear is always enabled - no toggle needed\n  \n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const { isMobile } = useResponsive();\n\n  // Create the popover content\n  const reminderPopover = (\n    <Popover id=\"reminder-popover\" style={{ maxWidth: '300px' }}>\n      <Popover.Header as=\"h3\">📸 Image Upload Guidelines</Popover.Header>\n      <Popover.Body>\n        <p style={{ marginBottom: '10px' }}>\n          Please ensure your uploaded images are:\n        </p>\n        <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>\n          <li>Focused directly on the road surface</li>\n          <li>Well-lit and clear</li>\n          <li>Showing the entire area of concern</li>\n          <li>Taken from a reasonable distance to capture context</li>\n        </ul>\n      </Popover.Body>\n    </Popover>\n  );\n\n  // Safari-compatible geolocation permission check\n  const checkLocationPermission = async () => {\n    if (!navigator.permissions || !navigator.permissions.query) {\n      // Fallback for older browsers\n      return 'prompt';\n    }\n    \n    try {\n      const permission = await navigator.permissions.query({ name: 'geolocation' });\n      return permission.state;\n    } catch (err) {\n      console.warn('Permission API not supported or failed:', err);\n      return 'prompt';\n    }\n  };\n\n  // Safari-compatible geolocation request\n  const requestLocation = () => {\n    return new Promise((resolve, reject) => {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by this browser'));\n        return;\n      }\n\n      // Check if we're in a secure context (HTTPS)\n      if (!window.isSecureContext) {\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\n        return;\n      }\n\n      const options = {\n        enableHighAccuracy: true,\n        timeout: 15000, // 15 seconds timeout\n        maximumAge: 60000 // Accept cached position up to 1 minute old\n      };\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          resolve(position);\n        },\n        (error) => {\n          let errorMessage = 'Unable to retrieve location';\n          \n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\n              break;\n            case error.POSITION_UNAVAILABLE:\n              errorMessage = 'Location information is unavailable. Please try again.';\n              break;\n            case error.TIMEOUT:\n              errorMessage = 'Location request timed out. Please try again.';\n              break;\n            default:\n              errorMessage = `Location error: ${error.message}`;\n              break;\n          }\n          \n          reject(new Error(errorMessage));\n        },\n        options\n      );\n    });\n  };\n\n  // Enhanced location handler with Safari-specific fixes\n  const handleLocationRequest = async () => {\n    setLocationLoading(true);\n    setLocationError('');\n    \n    try {\n      // First check permission state\n      const permissionState = await checkLocationPermission();\n      setLocationPermission(permissionState);\n      \n      // If permission is denied, provide user guidance\n      if (permissionState === 'denied') {\n        const errorMsg = 'Location access denied. To enable location access:\\n' +\n                        '• Safari: Settings > Privacy & Security > Location Services\\n' +\n                        '• Chrome: Settings > Privacy > Location\\n' +\n                        '• Firefox: Settings > Privacy > Location\\n' +\n                        'Then refresh this page and try again.';\n        setLocationError(errorMsg);\n        setCoordinates('Permission Denied');\n        return;\n      }\n      \n      // Request location\n      const position = await requestLocation();\n      const { latitude, longitude } = position.coords;\n      \n      // Format coordinates with better precision\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\n      setCoordinates(formattedCoords);\n      setLocationPermission('granted');\n      setLocationError('');\n      \n      console.log('Location acquired:', { latitude, longitude, accuracy: position.coords.accuracy });\n      \n    } catch (error) {\n      console.error('Location request failed:', error);\n      setLocationError(error.message);\n      setCoordinates('Location Error');\n      \n      // Update permission state based on error\n      if (error.message.includes('denied')) {\n        setLocationPermission('denied');\n      }\n    } finally {\n      setLocationLoading(false);\n    }\n  };\n\n  // Handle multiple file input change\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      setImageFiles([...imageFiles, ...files]);\n      \n      // Create previews and location data for each file\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreviewsMap(prev => ({\n            ...prev,\n            [file.name]: reader.result\n          }));\n        };\n        reader.readAsDataURL(file);\n        \n        // Store location as \"Not Available\" for uploaded files\n        setImageLocationMap(prev => ({\n          ...prev,\n          [file.name]: 'Not Available'\n        }));\n      });\n      \n      // Reset results\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n    }\n  };\n\n  // Handle camera capture with location validation\n  const handleCapture = async () => {\n    const imageSrc = webcamRef.current.getScreenshot();\n    if (imageSrc) {\n      // If we don't have location data, try to get it before capturing\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\n        await handleLocationRequest();\n      }\n      \n      const timestamp = new Date().toISOString();\n      const filename = `camera_capture_${timestamp}.jpg`;\n      const captureCoordinates = coordinates; // Capture current coordinates\n      \n      setImageFiles([...imageFiles, filename]);\n      setImagePreviewsMap(prev => ({\n        ...prev,\n        [filename]: imageSrc\n      }));\n      setImageLocationMap(prev => ({\n        ...prev,\n        [filename]: captureCoordinates\n      }));\n      setCurrentImageIndex(imageFiles.length);\n      \n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n      \n      // Log capture with current coordinates\n      console.log('Photo captured with coordinates:', captureCoordinates);\n    }\n  };\n\n  // Get location data for currently selected image\n  const getCurrentImageLocation = () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      return coordinates; // Use current coordinates if no images\n    }\n    \n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n    return imageLocationMap[currentFilename] || 'Not Available';\n  };\n\n  // Toggle camera with improved location handling\n  const toggleCamera = async () => {\n    const newCameraState = !cameraActive;\n    setCameraActive(newCameraState);\n    \n    if (newCameraState) {\n      // Get location when camera is activated\n      await handleLocationRequest();\n    } else {\n      // Only reset location if no images are captured\n      // This preserves location data for captured images\n      if (Object.keys(imagePreviewsMap).length === 0) {\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n      }\n    }\n  };\n\n  // Toggle camera orientation (front/back) for mobile devices\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Helper function to handle classification errors\n  const handleClassificationError = (errorMessage) => {\n    setClassificationError(errorMessage);\n    setShowClassificationModal(true);\n    setError(''); // Clear general error since we're showing specific modal\n  };\n\n  // Process image for detection\n  const handleProcess = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      // Get user info from session storage\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n\n      // Get the currently selected image\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\n\n      if (!currentImagePreview) {\n        setError('No image selected for processing');\n        setLoading(false);\n        return;\n      }\n\n      // Get coordinates for the current image\n      const imageCoordinates = getCurrentImageLocation();\n\n      // Get the current image filename\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Prepare request data\n      const requestData = {\n        image: currentImagePreview,\n        coordinates: imageCoordinates,\n        username: user?.username || 'Unknown',\n        role: user?.role || 'Unknown',\n        skip_road_classification: !roadClassificationEnabled\n      };\n\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch(detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n\n      // Make API request\n      const response = await axios.post(endpoint, requestData);\n\n      // Handle response\n      if (response.data.success) {\n        // Check if the image was actually processed (contains road) or just classified\n        const isProcessed = response.data.processed !== false;\n        const isRoad = response.data.classification?.is_road || false;\n\n        // Set the processed image and results for display\n        setProcessedImage(response.data.processed_image);\n        setResults(response.data);\n\n        // Extract detailed detection results for table display\n        const detectionResults = {\n          potholes: response.data.potholes || [],\n          cracks: response.data.cracks || [],\n          kerbs: response.data.kerbs || []\n        };\n\n        // Create batch result entry for the status table\n        const batchResult = {\n          filename: currentFilename,\n          success: true,\n          processed: isProcessed,\n          isRoad: isRoad,\n          classification: response.data.classification,\n          processedImage: response.data.processed_image,\n          data: response.data,\n          detectionResults: detectionResults,\n          detectionCounts: {\n            potholes: detectionResults.potholes.length,\n            cracks: detectionResults.cracks.length,\n            kerbs: detectionResults.kerbs.length,\n            total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n          }\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        // Auto-clear uploaded image icons after successful single image processing\n        // Store the processed image data before clearing (for both road and non-road)\n        setProcessedImagesData(prev => ({\n          ...prev,\n          [currentFilename]: {\n            originalImage: currentImagePreview,\n            processedImage: isRoad ? response.data.processed_image : null,\n            results: response.data,\n            isRoad: isRoad\n          }\n        }));\n          \n          // Clear image previews and files but keep results\n          setImageFiles([]);\n          setImagePreviewsMap({});\n          setImageLocationMap({});\n          setCurrentImageIndex(0);\n          \n          // Reset coordinates when clearing all images\n          setCoordinates('Not Available');\n          setLocationError('');\n          setLocationPermission('unknown');\n          \n          if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n          }\n      } else {\n        const errorMessage = response.data.message || 'Detection failed';\n\n        // Create batch result entry for failed processing\n        const batchResult = {\n          filename: currentFilename,\n          success: false,\n          processed: false,\n          isRoad: false,\n          error: errorMessage,\n          isClassificationError: errorMessage.includes('No road detected')\n        };\n\n        // Update batch results to show the status table\n        setBatchResults([batchResult]);\n\n        setError(errorMessage);\n      }\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'An error occurred during detection. Please try again.';\n\n      // Get the current image filename for batch results\n      const filenames = Object.keys(imagePreviewsMap);\n      const currentFilename = filenames[currentImageIndex];\n\n      // Create batch result entry for error case\n      const batchResult = {\n        filename: currentFilename,\n        success: false,\n        processed: false,\n        isRoad: false,\n        error: errorMessage,\n        isClassificationError: errorMessage.includes('No road detected')\n      };\n\n      // Update batch results to show the status table\n      setBatchResults([batchResult]);\n\n      // Check if this is a classification error (no road detected)\n      if (errorMessage.includes('No road detected')) {\n        handleClassificationError(errorMessage);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add a new function to process all images\n  const handleProcessAll = async () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      setError('No images to process');\n      return;\n    }\n\n    setBatchProcessing(true);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\n    \n    // Get user info from session storage\n    const userString = sessionStorage.getItem('user');\n    const user = userString ? JSON.parse(userString) : null;\n    \n    try {\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch(detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n      \n      const results = [];\n      const filenames = Object.keys(imagePreviewsMap);\n      \n      // Process each image sequentially and display immediately\n      for (let i = 0; i < filenames.length; i++) {\n        const filename = filenames[i];\n        const imageData = imagePreviewsMap[filename];\n        \n        try {\n          // Update current image index to show which image is being processed\n          setCurrentImageIndex(i);\n          \n          // Get coordinates for this specific image\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\n          \n          // Prepare request data\n          const requestData = {\n            image: imageData,\n            coordinates: imageCoordinates,\n            username: user?.username || 'Unknown',\n            role: user?.role || 'Unknown',\n            skip_road_classification: !roadClassificationEnabled\n          };\n          \n          // Make API request\n          const response = await axios.post(endpoint, requestData);\n          \n          if (response.data.success) {\n            // Check if the image was actually processed (contains road) or just classified\n            const isProcessed = response.data.processed !== false;\n            const isRoad = response.data.classification?.is_road || false;\n\n            if (isProcessed && isRoad) {\n              // Road image that was processed - display the results\n              setProcessedImage(response.data.processed_image);\n              setResults(response.data);\n            }\n\n            // Extract detailed detection results for table display\n            const detectionResults = {\n              potholes: response.data.potholes || [],\n              cracks: response.data.cracks || [],\n              kerbs: response.data.kerbs || []\n            };\n\n            results.push({\n              filename,\n              success: true,\n              processed: isProcessed,\n              isRoad: isRoad,\n              classification: response.data.classification,\n              processedImage: response.data.processed_image,\n              data: response.data,\n              detectionResults: detectionResults,\n              detectionCounts: {\n                potholes: detectionResults.potholes.length,\n                cracks: detectionResults.cracks.length,\n                kerbs: detectionResults.kerbs.length,\n                total: detectionResults.potholes.length + detectionResults.cracks.length + detectionResults.kerbs.length\n              }\n            });\n          } else {\n            const errorMessage = response.data.message || 'Detection failed';\n            results.push({\n              filename,\n              success: false,\n              processed: false,\n              isRoad: false,\n              error: errorMessage,\n              isClassificationError: errorMessage.includes('No road detected')\n            });\n          }\n        } catch (error) {\n          const errorMessage = error.response?.data?.message || 'An error occurred during detection';\n          results.push({\n            filename,\n            success: false,\n            processed: false,\n            isRoad: false,\n            error: errorMessage,\n            isClassificationError: errorMessage.includes('No road detected')\n          });\n        }\n        \n        // Update progress\n        setProcessedCount(prev => prev + 1);\n        \n        // Pause briefly to allow user to see the result before moving to next image\n        // Only pause if not on the last image\n        if (i < filenames.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\n        }\n      }\n      \n      // Store final results\n      setBatchResults(results);\n\n      // After batch processing is complete, display the first successfully processed road image\n      const processedRoadImages = results.filter(r => r.success && r.processed && r.isRoad);\n      if (processedRoadImages.length > 0) {\n        const firstProcessedRoadImage = processedRoadImages[0];\n        setProcessedImage(firstProcessedRoadImage.processedImage);\n        setResults(firstProcessedRoadImage.data);\n\n        // Set the current image index to 0 (first processed road image)\n        setCurrentImageIndex(0);\n      } else {\n        // No road images were processed, clear the display\n        setProcessedImage(null);\n        setResults(null);\n        setCurrentImageIndex(0);\n      }\n\n      // Auto-clear uploaded image icons after processing is complete\n      // Store processed images data before clearing (for both road and non-road)\n      const processedData = {};\n      results.forEach(result => {\n        if (result.success) {\n          const originalImage = imagePreviewsMap[result.filename];\n          processedData[result.filename] = {\n            originalImage: originalImage,\n            processedImage: result.isRoad ? result.processedImage : null,\n            results: result.data,\n            isRoad: result.isRoad\n          };\n          console.log('Storing image data for:', result.filename, 'isRoad:', result.isRoad, 'hasOriginalImage:', !!originalImage);\n        }\n      });\n      setProcessedImagesData(prev => ({ ...prev, ...processedData }));\n      \n      // Clear image previews and files but keep results\n      setImageFiles([]);\n      setImagePreviewsMap({});\n      setImageLocationMap({});\n      setCurrentImageIndex(0);\n      \n      // Reset coordinates when clearing all images\n      setCoordinates('Not Available');\n      setLocationError('');\n      setLocationPermission('unknown');\n      \n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n\n    } catch (error) {\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\n    } finally {\n      setBatchProcessing(false);\n    }\n  };\n\n  // Reset detection\n  const handleReset = () => {\n    setImageFiles([]);\n    setImagePreviewsMap({});\n    setImageLocationMap({});\n    setCurrentImageIndex(0);\n    setProcessedImage(null);\n    setResults(null);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(0);\n    setProcessedImagesData({});\n    \n    // Reset coordinates when clearing all images\n    setCoordinates('Not Available');\n    setLocationError('');\n    setLocationPermission('unknown');\n    \n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Helper function to get processed road images\n  const getProcessedRoadImages = () => {\n    return batchResults.filter(r => r.success && r.processed && r.isRoad);\n  };\n\n  // Helper function to get current processed image index\n  const getCurrentProcessedImageIndex = () => {\n    const processedImages = getProcessedRoadImages();\n    // If we have processed images and a current image index, use it directly\n    if (processedImages.length > 0 && currentImageIndex < processedImages.length) {\n      return currentImageIndex;\n    }\n    // Fallback: try to find by filename if imagePreviewsMap still has data\n    if (Object.keys(imagePreviewsMap).length > 0) {\n      const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n      return processedImages.findIndex(r => r.filename === currentFilename);\n    }\n    return 0; // Default to first image if no match found\n  };\n\n  // Add function to handle thumbnail clicks\n  const handleThumbnailClick = (imageData) => {\n    setSelectedImageData(imageData);\n    setShowImageModal(true);\n  };\n\n  // Add sorting function for detection results\n  const handleSort = (key) => {\n    let direction = 'asc';\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n    setSortConfig({ key, direction });\n  };\n\n  // Function to sort detection results\n  const sortDetections = (detections) => {\n    if (!sortConfig.key) return detections;\n\n    return [...detections].sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n\n      // Handle numeric values\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;\n      }\n\n      // Handle string values\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortConfig.direction === 'asc'\n          ? aValue.localeCompare(bValue)\n          : bValue.localeCompare(aValue);\n      }\n\n      // Handle null/undefined values\n      if (aValue == null && bValue == null) return 0;\n      if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\n      if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\n\n      return 0;\n    });\n  };\n\n  // Function to export detection results to CSV\n  const exportToCSV = () => {\n    // Flatten all detection results\n    const allDetections = [];\n\n    batchResults.forEach(result => {\n      if (result.success && result.processed && result.detectionResults) {\n        const { potholes, cracks, kerbs } = result.detectionResults;\n\n        // Add potholes\n        potholes.forEach(pothole => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Pothole',\n            id: pothole.pothole_id,\n            area_cm2: pothole.area_cm2,\n            depth_cm: pothole.depth_cm,\n            volume: pothole.volume,\n            volume_range: pothole.volume_range,\n            crack_type: '',\n            area_range: '',\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: pothole.confidence\n          });\n        });\n\n        // Add cracks\n        cracks.forEach(crack => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Crack',\n            id: crack.crack_id,\n            area_cm2: crack.area_cm2,\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: crack.crack_type,\n            area_range: crack.area_range,\n            kerb_type: '',\n            condition: '',\n            length_m: '',\n            confidence: crack.confidence\n          });\n        });\n\n        // Add kerbs\n        kerbs.forEach(kerb => {\n          allDetections.push({\n            filename: result.filename,\n            type: 'Kerb',\n            id: kerb.kerb_id,\n            area_cm2: '',\n            depth_cm: '',\n            volume: '',\n            volume_range: '',\n            crack_type: '',\n            area_range: '',\n            kerb_type: kerb.kerb_type,\n            condition: kerb.condition,\n            length_m: kerb.length_m,\n            confidence: kerb.confidence\n          });\n        });\n      }\n    });\n\n    if (allDetections.length === 0) {\n      alert('No detection results to export.');\n      return;\n    }\n\n    // Create CSV content\n    const headers = [\n      'Image Filename',\n      'Detection Type',\n      'ID',\n      'Area (cm²)',\n      'Depth (cm)',\n      'Volume (cm³)',\n      'Volume Range',\n      'Crack Type',\n      'Area Range',\n      'Kerb Type',\n      'Condition',\n      'Length (m)',\n      'Confidence'\n    ];\n\n    const csvContent = [\n      headers.join(','),\n      ...allDetections.map(detection => [\n        detection.filename,\n        detection.type,\n        detection.id || '',\n        detection.area_cm2 || '',\n        detection.depth_cm || '',\n        detection.volume || '',\n        detection.volume_range || '',\n        detection.crack_type || '',\n        detection.area_range || '',\n        detection.kerb_type || '',\n        detection.condition || '',\n        detection.length_m || '',\n        detection.confidence || ''\n      ].join(','))\n    ].join('\\n');\n\n    // Create and download file\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `pavement_detection_results_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // Add a function to handle auto-navigation through results\n  const startAutoNavigation = () => {\n    if (batchResults.length === 0) return;\n    \n    // Find only successful results\n    const successfulResults = batchResults.filter(result => result.success);\n    if (successfulResults.length === 0) return;\n    \n    setAutoNavigationActive(true);\n    setAutoNavigationIndex(0);\n    \n    // Display the first result\n    const firstResult = successfulResults[0];\n    setCurrentImageIndex(0);\n    setProcessedImage(firstResult.processedImage);\n    setResults(firstResult.data);\n    \n    // Set up interval for auto-navigation\n    autoNavigationRef.current = setInterval(() => {\n      setAutoNavigationIndex(prevIndex => {\n        const nextIndex = prevIndex + 1;\n        \n        // If we've reached the end, stop auto-navigation\n        if (nextIndex >= successfulResults.length) {\n          clearInterval(autoNavigationRef.current);\n          setAutoNavigationActive(false);\n          return prevIndex;\n        }\n        \n        // Display the next result\n        const nextResult = successfulResults[nextIndex];\n        setCurrentImageIndex(nextIndex);\n        setProcessedImage(nextResult.processedImage);\n        setResults(nextResult.data);\n        \n        return nextIndex;\n      });\n    }, 3000); // Change results every 3 seconds\n  };\n\n  // Clean up interval on component unmount\n  useEffect(() => {\n    return () => {\n      if (autoNavigationRef.current) {\n        clearInterval(autoNavigationRef.current);\n      }\n    };\n  }, []);\n\n  // Handle location permission changes\n  useEffect(() => {\n    if (cameraActive && locationPermission === 'unknown') {\n      // Try to get location when camera is first activated\n      handleLocationRequest();\n    }\n  }, [cameraActive]);\n\n  // Listen for permission changes if supported\n  useEffect(() => {\n    let permissionWatcher = null;\n    \n    const watchPermissions = async () => {\n      try {\n        if (navigator.permissions && navigator.permissions.query) {\n          const permission = await navigator.permissions.query({ name: 'geolocation' });\n          \n          permissionWatcher = () => {\n            setLocationPermission(permission.state);\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\n              handleLocationRequest();\n            }\n          };\n          \n          permission.addEventListener('change', permissionWatcher);\n        }\n      } catch (err) {\n        console.warn('Permission watching not supported:', err);\n      }\n    };\n    \n    watchPermissions();\n    \n    return () => {\n      if (permissionWatcher) {\n        try {\n          const permission = navigator.permissions.query({ name: 'geolocation' });\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\n        } catch (err) {\n          console.warn('Error removing permission listener:', err);\n        }\n      }\n    };\n  }, [cameraActive, coordinates]);\n\n  // Force re-render when current image changes to update location display\n  useEffect(() => {\n    // This effect ensures the UI updates when switching between images\n    // The getCurrentImageLocation function will return the correct location for the selected image\n  }, [currentImageIndex, imageLocationMap]);\n\n  // Stop auto-navigation\n  const stopAutoNavigation = () => {\n    if (autoNavigationRef.current) {\n      clearInterval(autoNavigationRef.current);\n      setAutoNavigationActive(false);\n    }\n  };\n\n  return (\n    <Container className=\"pavement-page\">\n      \n      <Tabs\n        activeKey={activeTab}\n        onSelect={(k) => setActiveTab(k)}\n        className=\"mb-3\"\n      >\n        <Tab eventKey=\"detection\" title=\"Image Detection\">\n          <Card className=\"mb-3\">\n            <Card.Body className=\"py-3\">\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"mb-1\">Detection Type</Form.Label>\n                <Form.Select \n                  value={detectionType}\n                  onChange={(e) => setDetectionType(e.target.value)}\n                >\n                  <option value=\"all\">All (Potholes + Cracks + Kerbs)</option>\n                  <option value=\"potholes\">Potholes</option>\n                  <option value=\"cracks\">Alligator Cracks</option>\n                  <option value=\"kerbs\">Kerbs</option>\n                </Form.Select>\n              </Form.Group>\n\n              {/* Sticky note reminder and road classification toggle */}\n              <div className=\"d-flex align-items-start gap-2 mb-3\">\n                <OverlayTrigger\n                  trigger=\"click\"\n                  placement=\"right\"\n                  overlay={reminderPopover}\n                  rootClose\n                >\n                  <div\n                    className=\"sticky-note-icon\"\n                    style={{ cursor: 'pointer', display: 'inline-block' }}\n                  >\n                    <img\n                      src=\"/remindericon.svg\"\n                      alt=\"Image Upload Guidelines\"\n                      style={{ width: '28px', height: '28px' }}\n                    />\n                  </div>\n                </OverlayTrigger>\n\n                {/* Road Classification Toggle - Improved Design */}\n                <div className=\"road-classification-control\">\n                  <div className=\"d-flex align-items-center justify-content-between mb-1\">\n                    <span className=\"me-2\" style={{ fontSize: '0.9rem', fontWeight: '500', color: '#495057' }}>\n                      Road Classification\n                    </span>\n                      <OverlayTrigger\n                        placement=\"right\"\n                        delay={{ show: 200, hide: 100 }}\n                        overlay={\n                          <Popover id=\"road-classification-detailed-info\" style={{ maxWidth: '350px' }}>\n                            <Popover.Header as=\"h3\">\n                              <i className=\"fas fa-brain me-2 text-primary\"></i>\n                              Road Classification Feature\n                            </Popover.Header>\n                            <Popover.Body>\n                              <div className=\"mb-2\">\n                                <div className=\"mb-1\">\n                                  <i className=\"fas fa-toggle-on text-success me-2\"></i>\n                                  <strong>ENABLED (ON):</strong>\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\n                                  • AI analyzes images for road content first<br/>\n                                  • Only road images get defect detection<br/>\n                                  • More accurate results, slightly slower\n                                </div>\n                              </div>\n\n                              <div className=\"mb-2\">\n                                <div className=\"mb-1\">\n                                  <i className=\"fas fa-toggle-off text-secondary me-2\"></i>\n                                  <strong>DISABLED (OFF):</strong>\n                                </div>\n                                <div style={{ fontSize: '12px', color: '#6c757d', marginLeft: '20px' }}>\n                                  • All images processed directly<br/>\n                                  • No road verification step<br/>\n                                  • Faster processing, may have false positives\n                                </div>\n                              </div>\n\n                              <div className=\"alert alert-info py-2 px-2 mb-0\" style={{ fontSize: '11px' }}>\n                                <i className=\"fas fa-lightbulb me-1\"></i>\n                                <strong>Recommendation:</strong> Keep enabled for mixed image types.\n                                Disable only when all images contain roads and speed is priority.\n                              </div>\n                            </Popover.Body>\n                          </Popover>\n                        }\n                      >\n                        <span className=\"info-icon-wrapper\">\n                          <span className=\"road-classification-info-icon\"\n                             style={{\n                               fontSize: '14px',\n                               cursor: 'help',\n                               color: '#007bff',\n                               display: 'inline-flex',\n                               alignItems: 'center',\n                               justifyContent: 'center',\n                               position: 'relative',\n                               zIndex: '1000',\n                               fontWeight: 'bold'\n                             }}\n                          >i</span>\n                        </span>\n                      </OverlayTrigger>\n                    </div>\n                    <div className=\"d-flex align-items-center\">\n                      <div\n                        className=\"toggle-switch me-2\"\n                        onClick={() => setRoadClassificationEnabled(!roadClassificationEnabled)}\n                        style={{\n                          width: '60px',\n                          height: '30px',\n                          backgroundColor: roadClassificationEnabled ? '#28a745' : '#6c757d',\n                          borderRadius: '15px',\n                          position: 'relative',\n                          cursor: 'pointer',\n                          transition: 'background-color 0.3s ease',\n                          border: '2px solid transparent'\n                        }}\n                      >\n                        <div\n                          className=\"toggle-slider\"\n                          style={{\n                            width: '22px',\n                            height: '22px',\n                            backgroundColor: 'white',\n                            borderRadius: '50%',\n                            position: 'absolute',\n                            top: '2px',\n                            left: roadClassificationEnabled ? '34px' : '2px',\n                            transition: 'left 0.3s ease',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'\n                          }}\n                        />\n                        <span\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: roadClassificationEnabled ? '8px' : '32px',\n                            transform: 'translateY(-50%)',\n                            fontSize: '10px',\n                            fontWeight: '600',\n                            color: 'white',\n                            transition: 'all 0.3s ease',\n                            userSelect: 'none'\n                          }}\n                        >\n                          {roadClassificationEnabled ? 'ON' : 'OFF'}\n                        </span>\n                      </div>\n                      <small className=\"text-muted\" style={{ fontSize: '11px' }}>\n                        {roadClassificationEnabled ? \"Only road images processed\" : \"All images processed\"}\n                      </small>\n                    </div>\n                  </div>\n                  \n\n                </div>\n\n              <div className=\"mb-3\">\n                <Form.Label>Image Source</Form.Label>\n                <div className=\"d-flex gap-2 mb-2\">\n                  <Button \n                    variant={cameraActive ? \"primary\" : \"outline-primary\"}\n                    onClick={toggleCamera}\n                    disabled={locationLoading}\n                  >\n                    {locationLoading ? (\n                      <>\n                        <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                        <span className=\"ms-2\">Getting Location...</span>\n                      </>\n                    ) : (\n                      cameraActive ? \"Disable Camera\" : \"Enable Camera\"\n                    )}\n                  </Button>\n                  <div className=\"file-input-container\">\n                    <label className=\"file-input-label\">\n                      Upload Image\n                      <input\n                        type=\"file\"\n                        className=\"file-input\"\n                        accept=\"image/*\"\n                        onChange={handleFileChange}\n                        ref={fileInputRef}\n                        disabled={cameraActive}\n                        multiple\n                      />\n                    </label>\n                  </div>\n                </div>\n                \n                {/* Location Status Display */}\n                {cameraActive && (\n                  <div className=\"location-status mb-3\">\n                    <small className=\"text-muted\">\n                      <strong>Location Status:</strong> \n                      {locationPermission === 'granted' && <span className=\"text-success ms-1\">✓ Enabled</span>}\n                      {locationPermission === 'denied' && <span className=\"text-danger ms-1\">✗ Denied</span>}\n                      {locationPermission === 'prompt' && <span className=\"text-warning ms-1\">⚠ Requesting...</span>}\n                      {locationPermission === 'unknown' && <span className=\"text-secondary ms-1\">? Unknown</span>}\n                    </small>\n                    {(coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && (\n                      <div className=\"mt-1\">\n                        <small className=\"text-muted\">\n                          <strong>Current Location:</strong> <span className=\"text-primary\">{coordinates}</span>\n                        </small>\n                        {Object.keys(imagePreviewsMap).length > 0 && (\n                          <div className=\"mt-1\">\n                            <small className=\"text-muted\">\n                              <strong>Selected Image Location:</strong> <span className=\"text-primary\">{getCurrentImageLocation()}</span>\n                            </small>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                    {locationError && (\n                      <Alert variant=\"warning\" className=\"mt-2 mb-0\" style={{ fontSize: '0.875rem' }}>\n                        <Alert.Heading as=\"h6\">Location Access Issue</Alert.Heading>\n                        <div style={{ whiteSpace: 'pre-line' }}>{locationError}</div>\n                        <hr />\n                        <div className=\"d-flex justify-content-end\">\n                          <Button variant=\"outline-warning\" size=\"sm\" onClick={handleLocationRequest}>\n                            Retry Location Access\n                          </Button>\n                        </div>\n                      </Alert>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {cameraActive && (\n                <div className=\"webcam-container mb-3\">\n                  <Webcam\n                    audio={false}\n                    ref={webcamRef}\n                    screenshotFormat=\"image/jpeg\"\n                    className=\"webcam\"\n                    videoConstraints={{\n                      width: 640,\n                      height: 480,\n                      facingMode: cameraOrientation\n                    }}\n                  />\n                  {isMobile && (\n                    <Button \n                      variant=\"outline-secondary\" \n                      onClick={toggleCameraOrientation}\n                      className=\"mt-2 mb-2\"\n                      size=\"sm\"\n                    >\n                      Rotate Camera\n                    </Button>\n                  )}\n                  <Button \n                    variant=\"success\" \n                    onClick={handleCapture}\n                    className=\"mt-2\"\n                  >\n                    Capture Photo\n                  </Button>\n                </div>\n              )}\n\n              {Object.keys(imagePreviewsMap).length > 0 && (\n                <div className=\"image-preview-container mb-3\">\n                  <h5>Previews</h5>\n                  <div className=\"image-gallery\">\n                    {Object.entries(imagePreviewsMap).map(([name, preview], index) => (\n                      <div \n                        key={name} \n                        className={`image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`}\n                        onClick={() => setCurrentImageIndex(index)}\n                      >\n                        <img \n                          src={preview} \n                          alt={`Preview ${index + 1}`} \n                          className=\"img-thumbnail\" \n                        />\n                        <button \n                          className=\"btn btn-sm btn-danger remove-image\" \n                          onClick={(e) => {\n                            e.stopPropagation();\n                            const newFiles = imageFiles.filter((_, i) => i !== index);\n                            const newPreviewsMap = {...imagePreviewsMap};\n                            const newLocationMap = {...imageLocationMap};\n                            delete newPreviewsMap[name];\n                            delete newLocationMap[name];\n                            setImageFiles(newFiles);\n                            setImagePreviewsMap(newPreviewsMap);\n                            setImageLocationMap(newLocationMap);\n                            if (currentImageIndex >= newFiles.length) {\n                              setCurrentImageIndex(Math.max(0, newFiles.length - 1));\n                            }\n                          }}\n                        >\n                          ×\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"current-image-preview\">\n                    {Object.values(imagePreviewsMap)[currentImageIndex] && (\n                      <img \n                        src={Object.values(imagePreviewsMap)[currentImageIndex]} \n                        alt=\"Current Preview\" \n                        className=\"image-preview img-fluid\" \n                      />\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n\n              <div className=\"d-flex gap-2 mb-3\">\n                <Button \n                  variant=\"primary\" \n                  onClick={handleProcess}\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                      <span className=\"ms-2\">Detecting...</span>\n                    </>\n                  ) : (\n                    `Detect Current Image`\n                  )}\n                </Button>\n                \n                <Button \n                  variant=\"success\" \n                  onClick={handleProcessAll}\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\n                >\n                  {batchProcessing ? (\n                    <>\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\n                      <span className=\"ms-2\">Processing {processedCount}/{totalToProcess}</span>\n                    </>\n                  ) : (\n                    `Process All Images`\n                  )}\n                </Button>\n                \n                <Button \n                  variant=\"secondary\" \n                  onClick={handleReset}\n                  disabled={loading || batchProcessing}\n                >\n                  Reset\n                </Button>\n              </div>\n            </Card.Body>\n          </Card>\n\n\n          {/* Enhanced Detection Results Table */}\n          {batchResults.some(result => result.success && result.processed && result.detectionCounts?.total > 0) && (\n            <Card className=\"mb-4\">\n              <Card.Header>\n                <div className=\"d-flex justify-content-between align-items-center\">\n                  <h5 className=\"mb-0\">\n                    <i className=\"fas fa-table me-2\"></i>\n                    Detailed Detection Results\n                  </h5>\n                  <div className=\"d-flex gap-2\">\n                    <Button\n                      variant={showDetailedResults ? 'primary' : 'outline-primary'}\n                      size=\"sm\"\n                      onClick={() => setShowDetailedResults(!showDetailedResults)}\n                    >\n                      {showDetailedResults ? 'Hide Details' : 'Show Details'}\n                    </Button>\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      onClick={exportToCSV}\n                      title=\"Export results to CSV\"\n                    >\n                      <i className=\"fas fa-download me-1\"></i>\n                      Export CSV\n                    </Button>\n                  </div>\n                </div>\n              </Card.Header>\n              {showDetailedResults && (\n                <Card.Body>\n                  {/* Filter Controls */}\n                  <div className=\"mb-3\">\n                    <div className=\"d-flex gap-2 flex-wrap\">\n                      <Button\n                        variant={detectionTableFilter === 'all' ? 'primary' : 'outline-primary'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('all')}\n                      >\n                        All Detections\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'potholes' ? 'danger' : 'outline-danger'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('potholes')}\n                      >\n                        Potholes Only\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'cracks' ? 'warning' : 'outline-warning'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('cracks')}\n                      >\n                        Cracks Only\n                      </Button>\n                      <Button\n                        variant={detectionTableFilter === 'kerbs' ? 'info' : 'outline-info'}\n                        size=\"sm\"\n                        onClick={() => setDetectionTableFilter('kerbs')}\n                      >\n                        Kerbs Only\n                      </Button>\n                    </div>\n                  </div>\n\n                  {/* Summary Statistics */}\n                  <div className=\"mb-4 detection-summary-cards\">\n                    <div className=\"row\">\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-danger text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Potholes</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.potholes || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-warning text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Cracks</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.cracks || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-info text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Kerbs</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.kerbs || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-md-3\">\n                        <div className=\"card bg-success text-white\">\n                          <div className=\"card-body text-center py-2\">\n                            <h6 className=\"mb-1\">Total Detections</h6>\n                            <h4 className=\"mb-0\">\n                              {batchResults.reduce((sum, result) => sum + (result.detectionCounts?.total || 0), 0)}\n                            </h4>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Detailed Results Table */}\n                  <div className=\"table-responsive detection-table-container\">\n                    {(() => {\n                      // Flatten all detection results into a single array\n                      const allDetections = [];\n\n                      batchResults.forEach(result => {\n                        if (result.success && result.processed && result.detectionResults) {\n                          const { potholes, cracks, kerbs } = result.detectionResults;\n\n                          // Add potholes\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'potholes') {\n                            potholes.forEach(pothole => {\n                              allDetections.push({\n                                ...pothole,\n                                type: 'Pothole',\n                                filename: result.filename,\n                                detectionType: 'potholes'\n                              });\n                            });\n                          }\n\n                          // Add cracks\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'cracks') {\n                            cracks.forEach(crack => {\n                              allDetections.push({\n                                ...crack,\n                                type: 'Crack',\n                                filename: result.filename,\n                                detectionType: 'cracks'\n                              });\n                            });\n                          }\n\n                          // Add kerbs\n                          if (detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') {\n                            kerbs.forEach(kerb => {\n                              allDetections.push({\n                                ...kerb,\n                                type: 'Kerb',\n                                filename: result.filename,\n                                detectionType: 'kerbs'\n                              });\n                            });\n                          }\n                        }\n                      });\n\n                      if (allDetections.length === 0) {\n                        return (\n                          <div className=\"text-center py-4\">\n                            <p className=\"text-muted\">No detections found for the selected filter.</p>\n                          </div>\n                        );\n                      }\n\n                      return (\n                        <table className=\"table table-striped table-bordered\">\n                          <thead>\n                            <tr>\n                              <th>Original Image</th>\n                              <th>Processed Image</th>\n                              <th\n                                style={{ cursor: 'pointer' }}\n                                onClick={() => handleSort('detectionType')}\n                              >\n                                Type {sortConfig.key === 'detectionType' && (\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                )}\n                              </th>\n                              <th>ID</th>\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('area_cm2')}\n                                  >\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('depth_cm')}\n                                  >\n                                    Depth (cm) {sortConfig.key === 'depth_cm' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('volume')}\n                                  >\n                                    Volume (cm³) {sortConfig.key === 'volume' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th>Volume Range</th>\n                                </>\n                              )}\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('crack_type')}\n                                  >\n                                    Crack Type {sortConfig.key === 'crack_type' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('area_cm2')}\n                                  >\n                                    Area (cm²) {sortConfig.key === 'area_cm2' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th>Area Range</th>\n                                </>\n                              )}\n                              {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\n                                <>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('kerb_type')}\n                                  >\n                                    Kerb Type {sortConfig.key === 'kerb_type' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('condition')}\n                                  >\n                                    Condition {sortConfig.key === 'condition' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                  <th\n                                    style={{ cursor: 'pointer' }}\n                                    onClick={() => handleSort('length_m')}\n                                  >\n                                    Length (m) {sortConfig.key === 'length_m' && (\n                                      <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                    )}\n                                  </th>\n                                </>\n                              )}\n                              <th\n                                style={{ cursor: 'pointer' }}\n                                onClick={() => handleSort('confidence')}\n                              >\n                                Confidence {sortConfig.key === 'confidence' && (\n                                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'} ms-1`}></i>\n                                )}\n                              </th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {sortDetections(allDetections).map((detection, index) => (\n                              <tr key={`${detection.filename}-${detection.detectionType}-${index}`}>\n                                {/* Original Image Column */}\n                                <td style={{ width: '120px', textAlign: 'center' }}>\n                                  {(() => {\n                                    const originalImage = imagePreviewsMap[detection.filename];\n                                    return originalImage ? (\n                                      <div\n                                        className=\"image-thumbnail-container\"\n                                        onClick={() => handleThumbnailClick({\n                                          originalImage: originalImage,\n                                          processedImage: batchResults.find(r => r.filename === detection.filename)?.processedImage,\n                                          isRoad: batchResults.find(r => r.filename === detection.filename)?.isRoad,\n                                          filename: detection.filename\n                                        })}\n                                        title=\"Click to enlarge 🔍\"\n                                      >\n                                        <img\n                                          src={originalImage}\n                                          alt=\"Original\"\n                                          className=\"image-thumbnail\"\n                                        />\n                                        <div className=\"thumbnail-overlay\">\n                                          🔍\n                                        </div>\n                                      </div>\n                                    ) : (\n                                      <small className=\"text-muted\">No image</small>\n                                    );\n                                  })()}\n                                </td>\n\n                                {/* Processed Image Column */}\n                                <td style={{ width: '120px', textAlign: 'center' }}>\n                                  {(() => {\n                                    const result = batchResults.find(r => r.filename === detection.filename);\n                                    const processedImage = result?.processedImage;\n                                    const originalImage = imagePreviewsMap[detection.filename];\n\n                                    return processedImage && result?.isRoad ? (\n                                      <div\n                                        className=\"image-thumbnail-container\"\n                                        onClick={() => handleThumbnailClick({\n                                          originalImage: originalImage,\n                                          processedImage: processedImage,\n                                          isRoad: result.isRoad,\n                                          filename: detection.filename\n                                        })}\n                                        title=\"Click to enlarge 🔍\"\n                                      >\n                                        <img\n                                          src={processedImage}\n                                          alt=\"Processed\"\n                                          className=\"image-thumbnail\"\n                                        />\n                                        <div className=\"thumbnail-overlay\">\n                                          🔍\n                                        </div>\n                                      </div>\n                                    ) : (\n                                      <small className=\"text-muted\">No processed image</small>\n                                    );\n                                  })()}\n                                </td>\n                                <td>\n                                  <span className={`badge ${\n                                    detection.detectionType === 'potholes' ? 'bg-danger' :\n                                    detection.detectionType === 'cracks' ? 'bg-warning' : 'bg-info'\n                                  }`}>\n                                    {detection.type}\n                                  </span>\n                                </td>\n                                <td>{detection.pothole_id || detection.crack_id || detection.kerb_id || index + 1}</td>\n\n                                {/* Pothole-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'potholes') && (\n                                  <>\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.volume_range || 'N/A'}</td>\n                                  </>\n                                )}\n\n                                {/* Crack-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'cracks') && (\n                                  <>\n                                    <td>{detection.crack_type || 'N/A'}</td>\n                                    <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                    <td>{detection.area_range || 'N/A'}</td>\n                                  </>\n                                )}\n\n                                {/* Kerb-specific columns */}\n                                {(detectionTableFilter === 'all' || detectionTableFilter === 'kerbs') && (\n                                  <>\n                                    <td>{detection.kerb_type || 'N/A'}</td>\n                                    <td>{detection.condition || 'N/A'}</td>\n                                    <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\n                                  </>\n                                )}\n\n                                <td>{detection.confidence ? (detection.confidence * 100).toFixed(1) + '%' : 'N/A'}</td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      );\n                    })()}\n                  </div>\n                </Card.Body>\n              )}\n            </Card>\n          )}\n\n          {/* Batch processing status indicator */}\n          {batchProcessing && (\n            <div className=\"batch-processing-status mt-3\">\n              <div className=\"d-flex align-items-center\">\n                <div className=\"me-3\">\n                  <Spinner animation=\"border\" size=\"sm\" role=\"status\" />\n                </div>\n                <div>\n                  <h6 className=\"mb-1\">Processing images: {processedCount}/{totalToProcess}</h6>\n                  <div className=\"progress\" style={{ height: '10px' }}>\n                    <div \n                      className=\"progress-bar\" \n                      role=\"progressbar\" \n                      style={{ width: `${(processedCount / totalToProcess) * 100}%` }}\n                      aria-valuenow={processedCount}\n                      aria-valuemin=\"0\" \n                      aria-valuemax={totalToProcess}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n\n\n\n\n          {/* Batch Processing Summary */}\n          {!batchProcessing && batchResults.length > 0 && (() => {\n            const totalImages = batchResults.length;\n            const successfulImages = batchResults.filter(r => r.success).length;\n            const failedImages = batchResults.filter(r => !r.success).length;\n\n            let alertVariant = 'light';\n            let alertClass = '';\n\n            if (roadClassificationEnabled) {\n              // When classification is enabled, use road/non-road logic\n              const nonRoadImages = batchResults.filter(r => !r.isRoad).length;\n              const nonRoadPercentage = totalImages > 0 ? (nonRoadImages / totalImages) * 100 : 0;\n\n              if (totalImages > 0) {\n                if (nonRoadPercentage === 0) {\n                  // 100% road detection - Green\n                  alertVariant = 'success';\n                } else if (nonRoadPercentage === 100) {\n                  // 100% non-road detection - Red\n                  alertVariant = 'danger';\n                } else {\n                  // Combined detection (mixed results) - Light Orange\n                  alertVariant = 'warning';\n                  alertClass = 'summary-light-orange';\n                }\n              }\n            } else {\n              // When classification is disabled, use success/failure logic\n              if (failedImages === 0) {\n                // All successful - Green\n                alertVariant = 'success';\n              } else if (successfulImages === 0) {\n                // All failed - Red\n                alertVariant = 'danger';\n              } else {\n                // Mixed results - Warning\n                alertVariant = 'warning';\n              }\n            }\n\n            return (\n              <div className=\"batch-complete-status mt-4\">\n                <Alert variant={alertVariant} className={alertClass}>\n                  <i className=\"fas fa-check-circle me-2\"></i>\n                  Processed {batchResults.length} images.\n                  {roadClassificationEnabled ? (\n                    <>\n                      {batchResults.filter(r => r.success && r.processed).length} road images processed,\n                      {batchResults.filter(r => r.success && !r.processed).length} non-road images detected,\n                      {batchResults.filter(r => !r.success).length} failed.\n                    </>\n                  ) : (\n                    <>\n                      {batchResults.filter(r => r.success).length} images processed successfully,\n                      {batchResults.filter(r => !r.success).length} failed.\n                    </>\n                  )}\n                </Alert>\n              </div>\n            );\n          })()}\n\n          {/* Image Status Table - Only show when road classification is enabled */}\n          {!batchProcessing && batchResults.length > 0 && roadClassificationEnabled && (\n            <div className=\"image-status-table mt-4\">\n              <Card>\n                <Card.Header>\n                  <div className=\"d-flex justify-content-between align-items-center\">\n                    <h5 className=\"mb-0\">Image Processing Status</h5>\n                    <div className=\"filter-buttons\">\n                      <Button\n                        variant={imageFilter === 'all' ? 'primary' : 'outline-primary'}\n                        size=\"sm\"\n                        className=\"me-2\"\n                        onClick={() => setImageFilter('all')}\n                      >\n                        Show All Images\n                      </Button>\n                      <Button\n                        variant={imageFilter === 'road' ? 'success' : 'outline-success'}\n                        size=\"sm\"\n                        className=\"me-2\"\n                        onClick={() => setImageFilter('road')}\n                      >\n                        Show Only Road Images\n                      </Button>\n                      <Button\n                        variant={imageFilter === 'non-road' ? 'danger' : 'outline-danger'}\n                        size=\"sm\"\n                        onClick={() => setImageFilter('non-road')}\n                      >\n                        Show Only Non-Road Images\n                      </Button>\n                    </div>\n                  </div>\n                </Card.Header>\n                <Card.Body>\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-striped\">\n                      <thead>\n                        <tr>\n                          <th>Image</th>\n                          <th>Detection Status</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {batchResults\n                          .filter(result => {\n                            if (imageFilter === 'road') return result.isRoad;\n                            if (imageFilter === 'non-road') return !result.isRoad;\n                            return true; // 'all'\n                          })\n                          .map((result, index) => {\n                            const filename = result.filename;\n                            const isRoad = result.isRoad;\n                            \n                            // Get image from stored processed data\n                            let imagePreview = null;\n                            let imageData = null;\n                            \n                            if (processedImagesData[filename]) {\n                              // Use stored processed image data\n                              imagePreview = processedImagesData[filename].originalImage;\n                              imageData = processedImagesData[filename];\n                              console.log('Found stored data for:', filename, 'hasImage:', !!imagePreview);\n                            } else if (imagePreviewsMap[filename]) {\n                              // Fallback to current preview (for any remaining unprocessed images)\n                              imagePreview = imagePreviewsMap[filename];\n                              imageData = {\n                                originalImage: imagePreview,\n                                processedImage: null,\n                                results: null,\n                                isRoad: isRoad\n                              };\n                              console.log('Using fallback data for:', filename, 'hasImage:', !!imagePreview);\n                            } else {\n                              console.log('No image data found for:', filename);\n                            }\n\n                            return (\n                              <tr key={filename}>\n                                <td>\n                                  <div className=\"d-flex align-items-center\">\n                                    {imagePreview ? (\n                                      <img\n                                        src={imagePreview}\n                                        alt={`Thumbnail ${index + 1}`}\n                                        className=\"img-thumbnail me-2\"\n                                        style={{ \n                                          width: '60px', \n                                          height: '60px', \n                                          objectFit: 'cover',\n                                          cursor: 'pointer'\n                                        }}\n                                        onClick={() => handleThumbnailClick(imageData)}\n                                        title=\"Click to view full size\"\n                                      />\n                                    ) : (\n                                      <div \n                                        className=\"img-thumbnail me-2 d-flex align-items-center justify-content-center\"\n                                        style={{ \n                                          width: '60px', \n                                          height: '60px', \n                                          backgroundColor: '#f8f9fa',\n                                          border: '1px solid #dee2e6'\n                                        }}\n                                      >\n                                        <small className=\"text-muted\">No Image</small>\n                                      </div>\n                                    )}\n                                    <small className=\"text-muted\">{filename}</small>\n                                  </div>\n                                </td>\n                                <td>\n                                  <span className={`badge ${isRoad ? 'bg-success' : 'bg-danger'}`}>\n                                    {isRoad ? 'Road' : 'Non-Road'}\n                                  </span>\n                                </td>\n                              </tr>\n                            );\n                          })}\n                      </tbody>\n                    </table>\n                  </div>\n\n\n\n\n                </Card.Body>\n              </Card>\n            </div>\n          )}\n        </Tab>\n        \n        <Tab eventKey=\"video\" title=\"Video Detection\">\n          <VideoDefectDetection />\n        </Tab>\n        \n        <Tab eventKey=\"information\" title=\"Information\">\n          <Card>\n            <Card.Body>\n              <h4>About Pavement Analysis</h4>\n              <p>\n                The Pavement Analysis module uses advanced computer vision to detect and analyze \n                various types of pavement defects and features:\n              </p>\n              \n              <h5>1. Potholes</h5>\n              <p>\n                Potholes are bowl-shaped holes of various sizes in the road surface that can be a \n                serious hazard to vehicles. The system detects potholes and calculates:\n              </p>\n              <ul>\n                <li>Area in square centimeters</li>\n                <li>Depth in centimeters</li>\n                <li>Volume</li>\n                <li>Classification by size (Small, Medium, Large)</li>\n              </ul>\n              \n              <h5>2. Alligator Cracks</h5>\n              <p>\n                Alligator cracks are a series of interconnected cracks creating a pattern resembling \n                an alligator's scales. These indicate underlying structural weakness. The system \n                identifies multiple types of cracks including:\n              </p>\n              <ul>\n                <li>Alligator Cracks</li>\n                <li>Edge Cracks</li>\n                <li>Hairline Cracks</li>\n                <li>Longitudinal Cracks</li>\n                <li>Transverse Cracks</li>\n              </ul>\n              \n              <h5>3. Kerbs</h5>\n              <p>\n                Kerbs are raised edges along a street or path that define boundaries between roadways \n                and other areas. The system identifies different kerb conditions including:\n              </p>\n              <ul>\n                <li>Normal/Good Kerbs - Structurally sound and properly visible</li>\n                <li>Faded Kerbs - Reduced visibility due to worn paint or weathering</li>\n                <li>Damaged Kerbs - Physically damaged or broken kerbs requiring repair</li>\n              </ul>\n              \n              <h5>Location Services & GPS Data</h5>\n              <p>\n                When using the live camera option, the application can capture GPS coordinates \n                to provide precise geolocation data for detected defects. This helps in:\n              </p>\n              <ul>\n                <li>Accurately mapping defect locations</li>\n                <li>Creating location-based reports</li>\n                <li>Enabling field teams to find specific issues</li>\n                <li>Tracking defect patterns by geographic area</li>\n              </ul>\n              \n              <h6>Location Requirements:</h6>\n              <ul>\n                <li><strong>Secure Connection:</strong> Location services require HTTPS</li>\n                <li><strong>Browser Permissions:</strong> You must allow location access when prompted</li>\n                <li><strong>Safari Users:</strong> Enable location services in Safari settings</li>\n                <li><strong>Mobile Devices:</strong> Ensure location services are enabled in device settings</li>\n              </ul>\n              \n              <div className=\"alert alert-info\">\n                <h6><i className=\"fas fa-info-circle me-2\"></i>Troubleshooting Location Issues</h6>\n                <p><strong>If location access is denied:</strong></p>\n                <ul className=\"mb-2\">\n                  <li><strong>Safari:</strong> Settings → Privacy & Security → Location Services</li>\n                  <li><strong>Chrome:</strong> Settings → Privacy and security → Site Settings → Location</li>\n                  <li><strong>Firefox:</strong> Settings → Privacy & Security → Permissions → Location</li>\n                </ul>\n                <p><strong>On mobile devices:</strong> Also check your device's location settings and ensure the browser has location permission.</p>\n              </div>\n\n              <h5>How to Use This Module</h5>\n              <ol>\n                <li>Select the detection type (Potholes, Alligator Cracks, or Kerbs)</li>\n                <li>Upload an image or use the camera to capture a photo</li>\n                <li>If using the camera, allow location access when prompted for GPS coordinates</li>\n                <li>Click the Detect button to analyze the image</li>\n                <li>Review the detection results and measurements</li>\n              </ol>\n              \n              <p>\n                The detected defects are automatically recorded in the database for tracking \n                and analysis in the Dashboard module.\n              </p>\n            </Card.Body>\n          </Card>\n        </Tab>\n      </Tabs>\n\n      {/* Classification Error Modal */}\n      {/* / <Modal\n        show={showClassificationModal}\n        onHide={() => setShowClassificationModal(false)}\n        centered\n      >\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-exclamation-triangle text-warning me-2\"></i>\n            Road Detection Failed\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div className=\"text-center\">\n            <div className=\"mb-3\">\n              <i className=\"fas fa-road fa-3x text-muted\"></i>\n            </div>\n            <h5 className=\"text-danger mb-3\">No Road Detected</h5>\n            <p className=\"mb-3\">\n              {classificationError || 'The uploaded image does not appear to contain a road. Please upload an image that clearly shows a road surface for defect detection.'}\n            </p>\n            <div className=\"alert alert-info\">\n              <strong>Tips for better results:</strong>\n              <ul className=\"mb-0 mt-2 text-start\">\n                <li>Ensure the image clearly shows a road surface</li>\n                <li>Avoid images with only buildings, sky, or vegetation</li>\n                <li>Make sure the road takes up a significant portion of the image</li>\n                <li>Use good lighting conditions for clearer road visibility</li>\n              </ul>\n            </div>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"primary\"\n            onClick={() => setShowClassificationModal(false)}\n          >\n            Try Another Image\n          </Button>\n        </Modal.Footer>\n      </Modal> */}\n\n      {/* Image Modal for Full-Size View */}\n      <Modal\n        show={showImageModal}\n        onHide={() => setShowImageModal(false)}\n        size=\"lg\"\n        centered\n      >\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-image me-2\"></i>\n            Image View {selectedImageData?.filename && (\n              <small className=\"text-muted\">- {selectedImageData.filename}</small>\n            )}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedImageData && (\n            <div className=\"text-center\">\n              <div className=\"mb-4\">\n                <h6 className=\"mb-3\">\n                  <i className=\"fas fa-camera me-2\"></i>\n                  Original Image\n                </h6>\n                <img\n                  src={selectedImageData.originalImage}\n                  alt=\"Original Image\"\n                  className=\"img-fluid\"\n                  style={{\n                    maxHeight: '400px',\n                    borderRadius: '8px',\n                    border: '2px solid #dee2e6',\n                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                  }}\n                />\n              </div>\n              {selectedImageData.processedImage && selectedImageData.isRoad && (\n                <div className=\"mt-4\">\n                  <h6 className=\"mb-3\">\n                    <i className=\"fas fa-search me-2\"></i>\n                    Processed Image (Detection Results)\n                  </h6>\n                  <img\n                    src={selectedImageData.processedImage}\n                    alt=\"Processed Image\"\n                    className=\"img-fluid\"\n                    style={{\n                      maxHeight: '400px',\n                      borderRadius: '8px',\n                      border: '2px solid #28a745',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }}\n                  />\n                </div>\n              )}\n              {!selectedImageData.isRoad && (\n                <div className=\"mt-3\">\n                  <Alert variant=\"info\">\n                    <i className=\"fas fa-info-circle me-2\"></i>\n                    This image was classified as non-road and therefore no defect detection was performed.\n                  </Alert>\n                </div>\n              )}\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => setShowImageModal(false)}\n          >\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\n// Add CSS styles for the enhanced detection table\nconst styles = `\n  .detection-table-container {\n    max-height: 600px;\n    overflow-y: auto;\n  }\n\n  .detection-table-container th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 10;\n  }\n\n  .detection-table-container th:hover {\n    background-color: #e9ecef;\n  }\n\n  .detection-summary-cards .card {\n    transition: transform 0.2s ease-in-out;\n  }\n\n  .detection-summary-cards .card:hover {\n    transform: translateY(-2px);\n  }\n\n  .table-responsive {\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  }\n\n  .badge {\n    font-size: 0.75em;\n  }\n\n  @media (max-width: 768px) {\n    .detection-summary-cards .col-md-3 {\n      margin-bottom: 1rem;\n    }\n\n    .d-flex.gap-2.flex-wrap {\n      flex-direction: column;\n    }\n\n    .d-flex.gap-2.flex-wrap .btn {\n      margin-bottom: 0.5rem;\n    }\n  }\n`;\n\n// Inject styles into the document head\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.type = 'text/css';\n  styleSheet.innerText = styles;\n  document.head.appendChild(styleSheet);\n}\n\nexport default Pavement;\n \n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC1H,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAO,gBAAgB;AACvB,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,SAASC,WAAW,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAACoD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrD,QAAQ,CAAC,SAAS,CAAC;EACvE,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACgE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElE;EACA,MAAM,CAACkE,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC;EACjE,MAAMkF,iBAAiB,GAAGjF,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAACkF,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;;EAEjF;EACA,MAAM,CAACqF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzE,MAAM,CAACuF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC;IAAE2F,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;;EAE7E;;EAEA,MAAMC,SAAS,GAAG5F,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM6F,YAAY,GAAG7F,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE8F;EAAS,CAAC,GAAG/E,aAAa,CAAC,CAAC;;EAEpC;EACA,MAAMgF,eAAe,gBACnB3E,OAAA,CAACT,OAAO;IAACqF,EAAE,EAAC,kBAAkB;IAACC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC1D/E,OAAA,CAACT,OAAO,CAACyF,MAAM;MAACC,EAAE,EAAC,IAAI;MAAAF,QAAA,EAAC;IAA0B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,eACnErF,OAAA,CAACT,OAAO,CAAC+F,IAAI;MAAAP,QAAA,gBACX/E,OAAA;QAAG6E,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEpC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrF,OAAA;QAAI6E,KAAK,EAAE;UAAEU,YAAY,EAAE,GAAG;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACpD/E,OAAA;UAAA+E,QAAA,EAAI;QAAoC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CrF,OAAA;UAAA+E,QAAA,EAAI;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BrF,OAAA;UAAA+E,QAAA,EAAI;QAAkC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CrF,OAAA;UAAA+E,QAAA,EAAI;QAAmD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACV;;EAED;EACA,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAACC,SAAS,CAACC,WAAW,IAAI,CAACD,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;MAC1D;MACA,OAAO,QAAQ;IACjB;IAEA,IAAI;MACF,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;QAAEE,IAAI,EAAE;MAAc,CAAC,CAAC;MAC7E,OAAOD,UAAU,CAACE,KAAK;IACzB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAC5D,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA,IAAI,CAACZ,SAAS,CAACa,WAAW,EAAE;QAC1BD,MAAM,CAAC,IAAIE,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACjE;MACF;;MAEA;MACA,IAAI,CAACC,MAAM,CAACC,eAAe,EAAE;QAC3BJ,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAClE;MACF;MAEA,MAAMG,OAAO,GAAG;QACdC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QAAE;QAChBC,UAAU,EAAE,KAAK,CAAC;MACpB,CAAC;MAEDpB,SAAS,CAACa,WAAW,CAACQ,kBAAkB,CACrCC,QAAQ,IAAK;QACZX,OAAO,CAACW,QAAQ,CAAC;MACnB,CAAC,EACAzF,KAAK,IAAK;QACT,IAAI0F,YAAY,GAAG,6BAA6B;QAEhD,QAAQ1F,KAAK,CAAC2F,IAAI;UAChB,KAAK3F,KAAK,CAAC4F,iBAAiB;YAC1BF,YAAY,GAAG,sFAAsF;YACrG;UACF,KAAK1F,KAAK,CAAC6F,oBAAoB;YAC7BH,YAAY,GAAG,wDAAwD;YACvE;UACF,KAAK1F,KAAK,CAAC8F,OAAO;YAChBJ,YAAY,GAAG,+CAA+C;YAC9D;UACF;YACEA,YAAY,GAAG,mBAAmB1F,KAAK,CAAC+F,OAAO,EAAE;YACjD;QACJ;QAEAhB,MAAM,CAAC,IAAIE,KAAK,CAACS,YAAY,CAAC,CAAC;MACjC,CAAC,EACDN,OACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCnF,kBAAkB,CAAC,IAAI,CAAC;IACxBF,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF;MACA,MAAMsF,eAAe,GAAG,MAAM/B,uBAAuB,CAAC,CAAC;MACvDzD,qBAAqB,CAACwF,eAAe,CAAC;;MAEtC;MACA,IAAIA,eAAe,KAAK,QAAQ,EAAE;QAChC,MAAMC,QAAQ,GAAG,sDAAsD,GACvD,+DAA+D,GAC/D,2CAA2C,GAC3C,4CAA4C,GAC5C,uCAAuC;QACvDvF,gBAAgB,CAACuF,QAAQ,CAAC;QAC1B7F,cAAc,CAAC,mBAAmB,CAAC;QACnC;MACF;;MAEA;MACA,MAAMoF,QAAQ,GAAG,MAAMb,eAAe,CAAC,CAAC;MACxC,MAAM;QAAEuB,QAAQ;QAAEC;MAAU,CAAC,GAAGX,QAAQ,CAACY,MAAM;;MAE/C;MACA,MAAMC,eAAe,GAAG,GAAGH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,KAAKH,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzElG,cAAc,CAACiG,eAAe,CAAC;MAC/B7F,qBAAqB,CAAC,SAAS,CAAC;MAChCE,gBAAgB,CAAC,EAAE,CAAC;MAEpB+D,OAAO,CAAC8B,GAAG,CAAC,oBAAoB,EAAE;QAAEL,QAAQ;QAAEC,SAAS;QAAEK,QAAQ,EAAEhB,QAAQ,CAACY,MAAM,CAACI;MAAS,CAAC,CAAC;IAEhG,CAAC,CAAC,OAAOzG,KAAK,EAAE;MACd0E,OAAO,CAAC1E,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDW,gBAAgB,CAACX,KAAK,CAAC+F,OAAO,CAAC;MAC/B1F,cAAc,CAAC,gBAAgB,CAAC;;MAEhC;MACA,IAAIL,KAAK,CAAC+F,OAAO,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACpCjG,qBAAqB,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM8F,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;IACxC,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpB9H,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAG2H,KAAK,CAAC,CAAC;;MAExC;MACAA,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;QACpB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvBjI,mBAAmB,CAACkI,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG6C,MAAM,CAACI;UACtB,CAAC,CAAC,CAAC;QACL,CAAC;QACDJ,MAAM,CAACK,aAAa,CAACN,IAAI,CAAC;;QAE1B;QACA5H,mBAAmB,CAACgI,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG;QACf,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;;MAEF;MACA5E,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMyH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMC,QAAQ,GAAG1E,SAAS,CAAC2E,OAAO,CAACC,aAAa,CAAC,CAAC;IAClD,IAAIF,QAAQ,EAAE;MACZ;MACA,IAAIvH,WAAW,KAAK,eAAe,IAAIA,WAAW,KAAK,gBAAgB,EAAE;QACvE,MAAM4F,qBAAqB,CAAC,CAAC;MAC/B;MAEA,MAAM8B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,kBAAkBH,SAAS,MAAM;MAClD,MAAMI,kBAAkB,GAAG9H,WAAW,CAAC,CAAC;;MAExCjB,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE+I,QAAQ,CAAC,CAAC;MACxC5I,mBAAmB,CAACkI,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGN;MACd,CAAC,CAAC,CAAC;MACHpI,mBAAmB,CAACgI,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGC;MACd,CAAC,CAAC,CAAC;MACHzI,oBAAoB,CAACP,UAAU,CAAC+H,MAAM,CAAC;MAEvCtH,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAyE,OAAO,CAAC8B,GAAG,CAAC,kCAAkC,EAAE0B,kBAAkB,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIC,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO7G,WAAW,CAAC,CAAC;IACtB;IAEA,MAAMkI,eAAe,GAAGF,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;IACxE,OAAOF,gBAAgB,CAACgJ,eAAe,CAAC,IAAI,eAAe;EAC7D,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,cAAc,GAAG,CAACtI,YAAY;IACpCC,eAAe,CAACqI,cAAc,CAAC;IAE/B,IAAIA,cAAc,EAAE;MAClB;MACA,MAAMxC,qBAAqB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL;MACA;MACA,IAAIoC,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,KAAK,CAAC,EAAE;QAC9C5G,cAAc,CAAC,eAAe,CAAC;QAC/BM,gBAAgB,CAAC,EAAE,CAAC;QACpBF,qBAAqB,CAAC,SAAS,CAAC;MAClC;IACF;EACF,CAAC;;EAED;EACA,MAAMgI,uBAAuB,GAAGA,CAAA,KAAM;IACpClI,oBAAoB,CAACgH,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMmB,yBAAyB,GAAIhD,YAAY,IAAK;IAClDjE,sBAAsB,CAACiE,YAAY,CAAC;IACpCnE,0BAA0B,CAAC,IAAI,CAAC;IAChCtB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAM0I,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC5I,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAM2I,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;MACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;;MAEvD;MACA,MAAMM,mBAAmB,GAAGd,MAAM,CAACe,MAAM,CAAC/J,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;MAE9E,IAAI,CAAC0J,mBAAmB,EAAE;QACxBjJ,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMqJ,gBAAgB,GAAGjB,uBAAuB,CAAC,CAAC;;MAElD;MACA,MAAMkB,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC;MAC/C,MAAMkJ,eAAe,GAAGe,SAAS,CAAC7J,iBAAiB,CAAC;;MAEpD;MACA,MAAM8J,WAAW,GAAG;QAClBC,KAAK,EAAEL,mBAAmB;QAC1B9I,WAAW,EAAEgJ,gBAAgB;QAC7BI,QAAQ,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAI,SAAS;QACrCC,IAAI,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,KAAI,SAAS;QAC7BC,wBAAwB,EAAE,CAACnH;MAC7B,CAAC;;MAED;MACA,IAAIoH,QAAQ;MACZ,QAAO3K,aAAa;QAClB,KAAK,KAAK;UACR2K,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;;MAEA;MACA,MAAMC,QAAQ,GAAG,MAAM1L,KAAK,CAAC2L,IAAI,CAACF,QAAQ,EAAEL,WAAW,CAAC;;MAExD;MACA,IAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAAA,IAAAC,qBAAA;QACzB;QACA,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,KAAK,KAAK;QACrD,MAAMC,MAAM,GAAG,EAAAH,qBAAA,GAAAJ,QAAQ,CAACE,IAAI,CAACM,cAAc,cAAAJ,qBAAA,uBAA5BA,qBAAA,CAA8BK,OAAO,KAAI,KAAK;;QAE7D;QACA1K,iBAAiB,CAACiK,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC;QAChDzK,UAAU,CAAC+J,QAAQ,CAACE,IAAI,CAAC;;QAEzB;QACA,MAAMS,gBAAgB,GAAG;UACvBC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,IAAI,EAAE;UACtCC,MAAM,EAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,IAAI,EAAE;UAClCC,KAAK,EAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,IAAI;QAChC,CAAC;;QAED;QACA,MAAMC,WAAW,GAAG;UAClB1C,QAAQ,EAAEK,eAAe;UACzByB,OAAO,EAAE,IAAI;UACbG,SAAS,EAAED,WAAW;UACtBE,MAAM,EAAEA,MAAM;UACdC,cAAc,EAAER,QAAQ,CAACE,IAAI,CAACM,cAAc;UAC5C1K,cAAc,EAAEkK,QAAQ,CAACE,IAAI,CAACQ,eAAe;UAC7CR,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBS,gBAAgB,EAAEA,gBAAgB;UAClCK,eAAe,EAAE;YACfJ,QAAQ,EAAED,gBAAgB,CAACC,QAAQ,CAACvD,MAAM;YAC1CwD,MAAM,EAAEF,gBAAgB,CAACE,MAAM,CAACxD,MAAM;YACtCyD,KAAK,EAAEH,gBAAgB,CAACG,KAAK,CAACzD,MAAM;YACpC4D,KAAK,EAAEN,gBAAgB,CAACC,QAAQ,CAACvD,MAAM,GAAGsD,gBAAgB,CAACE,MAAM,CAACxD,MAAM,GAAGsD,gBAAgB,CAACG,KAAK,CAACzD;UACpG;QACF,CAAC;;QAED;QACAlG,eAAe,CAAC,CAAC4J,WAAW,CAAC,CAAC;;QAE9B;QACA;QACAtJ,sBAAsB,CAACkG,IAAI,KAAK;UAC9B,GAAGA,IAAI;UACP,CAACe,eAAe,GAAG;YACjBwC,aAAa,EAAE5B,mBAAmB;YAClCxJ,cAAc,EAAEyK,MAAM,GAAGP,QAAQ,CAACE,IAAI,CAACQ,eAAe,GAAG,IAAI;YAC7D1K,OAAO,EAAEgK,QAAQ,CAACE,IAAI;YACtBK,MAAM,EAAEA;UACV;QACF,CAAC,CAAC,CAAC;;QAED;QACAhL,aAAa,CAAC,EAAE,CAAC;QACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvBE,oBAAoB,CAAC,CAAC,CAAC;;QAEvB;QACAY,cAAc,CAAC,eAAe,CAAC;QAC/BM,gBAAgB,CAAC,EAAE,CAAC;QACpBF,qBAAqB,CAAC,SAAS,CAAC;QAEhC,IAAIyC,YAAY,CAAC0E,OAAO,EAAE;UACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;QACjC;MACJ,CAAC,MAAM;QACL,MAAMrF,YAAY,GAAGkE,QAAQ,CAACE,IAAI,CAAC/D,OAAO,IAAI,kBAAkB;;QAEhE;QACA,MAAM4E,WAAW,GAAG;UAClB1C,QAAQ,EAAEK,eAAe;UACzByB,OAAO,EAAE,KAAK;UACdG,SAAS,EAAE,KAAK;UAChBC,MAAM,EAAE,KAAK;UACbnK,KAAK,EAAE0F,YAAY;UACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;QACjE,CAAC;;QAED;QACA3F,eAAe,CAAC,CAAC4J,WAAW,CAAC,CAAC;QAE9B1K,QAAQ,CAACyF,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAO1F,KAAK,EAAE;MAAA,IAAAiL,eAAA,EAAAC,oBAAA;MACd,MAAMxF,YAAY,GAAG,EAAAuF,eAAA,GAAAjL,KAAK,CAAC4J,QAAQ,cAAAqB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBnB,IAAI,cAAAoB,oBAAA,uBAApBA,oBAAA,CAAsBnF,OAAO,KAAI,uDAAuD;;MAE7G;MACA,MAAMsD,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC;MAC/C,MAAMkJ,eAAe,GAAGe,SAAS,CAAC7J,iBAAiB,CAAC;;MAEpD;MACA,MAAMmL,WAAW,GAAG;QAClB1C,QAAQ,EAAEK,eAAe;QACzByB,OAAO,EAAE,KAAK;QACdG,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAE,KAAK;QACbnK,KAAK,EAAE0F,YAAY;QACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;MACjE,CAAC;;MAED;MACA3F,eAAe,CAAC,CAAC4J,WAAW,CAAC,CAAC;;MAE9B;MACA,IAAIjF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC7CgC,yBAAyB,CAAChD,YAAY,CAAC;MACzC,CAAC,MAAM;QACLzF,QAAQ,CAACyF,YAAY,CAAC;MACxB;IACF,CAAC,SAAS;MACR3F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoL,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI/C,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,KAAK,CAAC,EAAE;MAC9ChH,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEAgB,kBAAkB,CAAC,IAAI,CAAC;IACxBhB,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBQ,iBAAiB,CAACyG,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,CAAC;;IAEvD;IACA,MAAM2B,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;IAEvD,IAAI;MACF;MACA,IAAIe,QAAQ;MACZ,QAAO3K,aAAa;QAClB,KAAK,KAAK;UACR2K,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;MAEA,MAAM/J,OAAO,GAAG,EAAE;MAClB,MAAMyJ,SAAS,GAAGjB,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC;;MAE/C;MACA,KAAK,IAAIgM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,SAAS,CAACpC,MAAM,EAAEmE,CAAC,EAAE,EAAE;QACzC,MAAMnD,QAAQ,GAAGoB,SAAS,CAAC+B,CAAC,CAAC;QAC7B,MAAMC,SAAS,GAAGjM,gBAAgB,CAAC6I,QAAQ,CAAC;QAE5C,IAAI;UACF;UACAxI,oBAAoB,CAAC2L,CAAC,CAAC;;UAEvB;UACA,MAAMhC,gBAAgB,GAAG9J,gBAAgB,CAAC2I,QAAQ,CAAC,IAAI,eAAe;;UAEtE;UACA,MAAMqB,WAAW,GAAG;YAClBC,KAAK,EAAE8B,SAAS;YAChBjL,WAAW,EAAEgJ,gBAAgB;YAC7BI,QAAQ,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAI,SAAS;YACrCC,IAAI,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,KAAI,SAAS;YAC7BC,wBAAwB,EAAE,CAACnH;UAC7B,CAAC;;UAED;UACA,MAAMqH,QAAQ,GAAG,MAAM1L,KAAK,CAAC2L,IAAI,CAACF,QAAQ,EAAEL,WAAW,CAAC;UAExD,IAAIM,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;YAAA,IAAAuB,sBAAA;YACzB;YACA,MAAMrB,WAAW,GAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,KAAK,KAAK;YACrD,MAAMC,MAAM,GAAG,EAAAmB,sBAAA,GAAA1B,QAAQ,CAACE,IAAI,CAACM,cAAc,cAAAkB,sBAAA,uBAA5BA,sBAAA,CAA8BjB,OAAO,KAAI,KAAK;YAE7D,IAAIJ,WAAW,IAAIE,MAAM,EAAE;cACzB;cACAxK,iBAAiB,CAACiK,QAAQ,CAACE,IAAI,CAACQ,eAAe,CAAC;cAChDzK,UAAU,CAAC+J,QAAQ,CAACE,IAAI,CAAC;YAC3B;;YAEA;YACA,MAAMS,gBAAgB,GAAG;cACvBC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACU,QAAQ,IAAI,EAAE;cACtCC,MAAM,EAAEb,QAAQ,CAACE,IAAI,CAACW,MAAM,IAAI,EAAE;cAClCC,KAAK,EAAEd,QAAQ,CAACE,IAAI,CAACY,KAAK,IAAI;YAChC,CAAC;YAED9K,OAAO,CAAC2L,IAAI,CAAC;cACXtD,QAAQ;cACR8B,OAAO,EAAE,IAAI;cACbG,SAAS,EAAED,WAAW;cACtBE,MAAM,EAAEA,MAAM;cACdC,cAAc,EAAER,QAAQ,CAACE,IAAI,CAACM,cAAc;cAC5C1K,cAAc,EAAEkK,QAAQ,CAACE,IAAI,CAACQ,eAAe;cAC7CR,IAAI,EAAEF,QAAQ,CAACE,IAAI;cACnBS,gBAAgB,EAAEA,gBAAgB;cAClCK,eAAe,EAAE;gBACfJ,QAAQ,EAAED,gBAAgB,CAACC,QAAQ,CAACvD,MAAM;gBAC1CwD,MAAM,EAAEF,gBAAgB,CAACE,MAAM,CAACxD,MAAM;gBACtCyD,KAAK,EAAEH,gBAAgB,CAACG,KAAK,CAACzD,MAAM;gBACpC4D,KAAK,EAAEN,gBAAgB,CAACC,QAAQ,CAACvD,MAAM,GAAGsD,gBAAgB,CAACE,MAAM,CAACxD,MAAM,GAAGsD,gBAAgB,CAACG,KAAK,CAACzD;cACpG;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMvB,YAAY,GAAGkE,QAAQ,CAACE,IAAI,CAAC/D,OAAO,IAAI,kBAAkB;YAChEnG,OAAO,CAAC2L,IAAI,CAAC;cACXtD,QAAQ;cACR8B,OAAO,EAAE,KAAK;cACdG,SAAS,EAAE,KAAK;cAChBC,MAAM,EAAE,KAAK;cACbnK,KAAK,EAAE0F,YAAY;cACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;YACjE,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAO1G,KAAK,EAAE;UAAA,IAAAwL,gBAAA,EAAAC,qBAAA;UACd,MAAM/F,YAAY,GAAG,EAAA8F,gBAAA,GAAAxL,KAAK,CAAC4J,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsB1F,OAAO,KAAI,oCAAoC;UAC1FnG,OAAO,CAAC2L,IAAI,CAAC;YACXtD,QAAQ;YACR8B,OAAO,EAAE,KAAK;YACdG,SAAS,EAAE,KAAK;YAChBC,MAAM,EAAE,KAAK;YACbnK,KAAK,EAAE0F,YAAY;YACnBsF,qBAAqB,EAAEtF,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;UACjE,CAAC,CAAC;QACJ;;QAEA;QACAvF,iBAAiB,CAACoG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;;QAEnC;QACA;QACA,IAAI6D,CAAC,GAAG/B,SAAS,CAACpC,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAIpC,OAAO,CAACC,OAAO,IAAI4G,UAAU,CAAC5G,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D;MACF;;MAEA;MACA/D,eAAe,CAACnB,OAAO,CAAC;;MAExB;MACA,MAAM+L,mBAAmB,GAAG/L,OAAO,CAACgM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI8B,CAAC,CAAC3B,SAAS,IAAI2B,CAAC,CAAC1B,MAAM,CAAC;MACrF,IAAIwB,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,EAAE;QAClC,MAAM6E,uBAAuB,GAAGH,mBAAmB,CAAC,CAAC,CAAC;QACtDhM,iBAAiB,CAACmM,uBAAuB,CAACpM,cAAc,CAAC;QACzDG,UAAU,CAACiM,uBAAuB,CAAChC,IAAI,CAAC;;QAExC;QACArK,oBAAoB,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QACL;QACAE,iBAAiB,CAAC,IAAI,CAAC;QACvBE,UAAU,CAAC,IAAI,CAAC;QAChBJ,oBAAoB,CAAC,CAAC,CAAC;MACzB;;MAEA;MACA;MACA,MAAMsM,aAAa,GAAG,CAAC,CAAC;MACxBnM,OAAO,CAACsH,OAAO,CAACM,MAAM,IAAI;QACxB,IAAIA,MAAM,CAACuC,OAAO,EAAE;UAClB,MAAMe,aAAa,GAAG1L,gBAAgB,CAACoI,MAAM,CAACS,QAAQ,CAAC;UACvD8D,aAAa,CAACvE,MAAM,CAACS,QAAQ,CAAC,GAAG;YAC/B6C,aAAa,EAAEA,aAAa;YAC5BpL,cAAc,EAAE8H,MAAM,CAAC2C,MAAM,GAAG3C,MAAM,CAAC9H,cAAc,GAAG,IAAI;YAC5DE,OAAO,EAAE4H,MAAM,CAACsC,IAAI;YACpBK,MAAM,EAAE3C,MAAM,CAAC2C;UACjB,CAAC;UACDzF,OAAO,CAAC8B,GAAG,CAAC,yBAAyB,EAAEgB,MAAM,CAACS,QAAQ,EAAE,SAAS,EAAET,MAAM,CAAC2C,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAACW,aAAa,CAAC;QACzH;MACF,CAAC,CAAC;MACFzJ,sBAAsB,CAACkG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGwE;MAAc,CAAC,CAAC,CAAC;;MAE/D;MACA5M,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACvBE,oBAAoB,CAAC,CAAC,CAAC;;MAEvB;MACAY,cAAc,CAAC,eAAe,CAAC;MAC/BM,gBAAgB,CAAC,EAAE,CAAC;MACpBF,qBAAqB,CAAC,SAAS,CAAC;MAEhC,IAAIyC,YAAY,CAAC0E,OAAO,EAAE;QACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;MACjC;IAEF,CAAC,CAAC,OAAO/K,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,IAAID,KAAK,CAAC+F,OAAO,IAAI,eAAe,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR9E,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM+K,WAAW,GAAGA,CAAA,KAAM;IACxB7M,aAAa,CAAC,EAAE,CAAC;IACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,oBAAoB,CAAC,CAAC,CAAC;IACvBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBQ,iBAAiB,CAAC,CAAC,CAAC;IACpBN,sBAAsB,CAAC,CAAC,CAAC,CAAC;;IAE1B;IACAhB,cAAc,CAAC,eAAe,CAAC;IAC/BM,gBAAgB,CAAC,EAAE,CAAC;IACpBF,qBAAqB,CAAC,SAAS,CAAC;IAEhC,IAAIyC,YAAY,CAAC0E,OAAO,EAAE;MACxB1E,YAAY,CAAC0E,OAAO,CAACmD,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMkB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,OAAOnL,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI8B,CAAC,CAAC3B,SAAS,IAAI2B,CAAC,CAAC1B,MAAM,CAAC;EACvE,CAAC;;EAED;EACA,MAAM+B,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,MAAMC,eAAe,GAAGF,sBAAsB,CAAC,CAAC;IAChD;IACA,IAAIE,eAAe,CAAClF,MAAM,GAAG,CAAC,IAAIzH,iBAAiB,GAAG2M,eAAe,CAAClF,MAAM,EAAE;MAC5E,OAAOzH,iBAAiB;IAC1B;IACA;IACA,IAAI4I,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMqB,eAAe,GAAGF,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;MACxE,OAAO2M,eAAe,CAACC,SAAS,CAACP,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKK,eAAe,CAAC;IACvE;IACA,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAM+D,oBAAoB,GAAIhB,SAAS,IAAK;IAC1CtJ,oBAAoB,CAACsJ,SAAS,CAAC;IAC/BxJ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyK,UAAU,GAAIvJ,GAAG,IAAK;IAC1B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIH,UAAU,CAACE,GAAG,KAAKA,GAAG,IAAIF,UAAU,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5DA,SAAS,GAAG,MAAM;IACpB;IACAF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMuJ,cAAc,GAAIC,UAAU,IAAK;IACrC,IAAI,CAAC3J,UAAU,CAACE,GAAG,EAAE,OAAOyJ,UAAU;IAEtC,OAAO,CAAC,GAAGA,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpC,IAAIC,MAAM,GAAGF,CAAC,CAAC7J,UAAU,CAACE,GAAG,CAAC;MAC9B,IAAI8J,MAAM,GAAGF,CAAC,CAAC9J,UAAU,CAACE,GAAG,CAAC;;MAE9B;MACA,IAAI,OAAO6J,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAOhK,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG4J,MAAM,GAAGC,MAAM,GAAGA,MAAM,GAAGD,MAAM;MAC3E;;MAEA;MACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAOhK,UAAU,CAACG,SAAS,KAAK,KAAK,GACjC4J,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC,GAC5BA,MAAM,CAACC,aAAa,CAACF,MAAM,CAAC;MAClC;;MAEA;MACA,IAAIA,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC;MAC9C,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO/J,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAClE,IAAI6J,MAAM,IAAI,IAAI,EAAE,OAAOhK,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAElE,OAAO,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+J,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,aAAa,GAAG,EAAE;IAExBlM,YAAY,CAACoG,OAAO,CAACM,MAAM,IAAI;MAC7B,IAAIA,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI1C,MAAM,CAAC+C,gBAAgB,EAAE;QACjE,MAAM;UAAEC,QAAQ;UAAEC,MAAM;UAAEC;QAAM,CAAC,GAAGlD,MAAM,CAAC+C,gBAAgB;;QAE3D;QACAC,QAAQ,CAACtD,OAAO,CAAC+F,OAAO,IAAI;UAC1BD,aAAa,CAACzB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzBiF,IAAI,EAAE,SAAS;YACf7J,EAAE,EAAE4J,OAAO,CAACE,UAAU;YACtBC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BC,QAAQ,EAAEJ,OAAO,CAACI,QAAQ;YAC1BC,MAAM,EAAEL,OAAO,CAACK,MAAM;YACtBC,YAAY,EAAEN,OAAO,CAACM,YAAY;YAClCC,UAAU,EAAE,EAAE;YACdC,UAAU,EAAE,EAAE;YACdC,SAAS,EAAE,EAAE;YACbC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAEZ,OAAO,CAACY;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACApD,MAAM,CAACvD,OAAO,CAAC4G,KAAK,IAAI;UACtBd,aAAa,CAACzB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzBiF,IAAI,EAAE,OAAO;YACb7J,EAAE,EAAEyK,KAAK,CAACC,QAAQ;YAClBX,QAAQ,EAAEU,KAAK,CAACV,QAAQ;YACxBC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAEM,KAAK,CAACN,UAAU;YAC5BC,UAAU,EAAEK,KAAK,CAACL,UAAU;YAC5BC,SAAS,EAAE,EAAE;YACbC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAEC,KAAK,CAACD;UACpB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACAnD,KAAK,CAACxD,OAAO,CAAC8G,IAAI,IAAI;UACpBhB,aAAa,CAACzB,IAAI,CAAC;YACjBtD,QAAQ,EAAET,MAAM,CAACS,QAAQ;YACzBiF,IAAI,EAAE,MAAM;YACZ7J,EAAE,EAAE2K,IAAI,CAACC,OAAO;YAChBb,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAE,EAAE;YACdC,UAAU,EAAE,EAAE;YACdC,SAAS,EAAEM,IAAI,CAACN,SAAS;YACzBC,SAAS,EAAEK,IAAI,CAACL,SAAS;YACzBC,QAAQ,EAAEI,IAAI,CAACJ,QAAQ;YACvBC,UAAU,EAAEG,IAAI,CAACH;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAIb,aAAa,CAAC/F,MAAM,KAAK,CAAC,EAAE;MAC9BiH,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACF;;IAEA;IACA,MAAMC,OAAO,GAAG,CACd,gBAAgB,EAChB,gBAAgB,EAChB,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,CACb;IAED,MAAMC,UAAU,GAAG,CACjBD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGrB,aAAa,CAACsB,GAAG,CAACC,SAAS,IAAI,CAChCA,SAAS,CAACtG,QAAQ,EAClBsG,SAAS,CAACrB,IAAI,EACdqB,SAAS,CAAClL,EAAE,IAAI,EAAE,EAClBkL,SAAS,CAACnB,QAAQ,IAAI,EAAE,EACxBmB,SAAS,CAAClB,QAAQ,IAAI,EAAE,EACxBkB,SAAS,CAACjB,MAAM,IAAI,EAAE,EACtBiB,SAAS,CAAChB,YAAY,IAAI,EAAE,EAC5BgB,SAAS,CAACf,UAAU,IAAI,EAAE,EAC1Be,SAAS,CAACd,UAAU,IAAI,EAAE,EAC1Bc,SAAS,CAACb,SAAS,IAAI,EAAE,EACzBa,SAAS,CAACZ,SAAS,IAAI,EAAE,EACzBY,SAAS,CAACX,QAAQ,IAAI,EAAE,EACxBW,SAAS,CAACV,UAAU,IAAI,EAAE,CAC3B,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;MAAElB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMwB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IACrCE,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,8BAA8B,IAAIjH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACiH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACzGP,IAAI,CAACpL,KAAK,CAAC4L,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC;IAC/BA,IAAI,CAACW,KAAK,CAAC,CAAC;IACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACZ,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIzO,YAAY,CAACmG,MAAM,KAAK,CAAC,EAAE;;IAE/B;IACA,MAAMuI,iBAAiB,GAAG1O,YAAY,CAAC8K,MAAM,CAACpE,MAAM,IAAIA,MAAM,CAACuC,OAAO,CAAC;IACvE,IAAIyF,iBAAiB,CAACvI,MAAM,KAAK,CAAC,EAAE;IAEpC9E,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,sBAAsB,CAAC,CAAC,CAAC;;IAEzB;IACA,MAAMoN,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACxC/P,oBAAoB,CAAC,CAAC,CAAC;IACvBE,iBAAiB,CAAC8P,WAAW,CAAC/P,cAAc,CAAC;IAC7CG,UAAU,CAAC4P,WAAW,CAAC3F,IAAI,CAAC;;IAE5B;IACAxH,iBAAiB,CAACsF,OAAO,GAAG8H,WAAW,CAAC,MAAM;MAC5CrN,sBAAsB,CAACsN,SAAS,IAAI;QAClC,MAAMC,SAAS,GAAGD,SAAS,GAAG,CAAC;;QAE/B;QACA,IAAIC,SAAS,IAAIJ,iBAAiB,CAACvI,MAAM,EAAE;UACzC4I,aAAa,CAACvN,iBAAiB,CAACsF,OAAO,CAAC;UACxCzF,uBAAuB,CAAC,KAAK,CAAC;UAC9B,OAAOwN,SAAS;QAClB;;QAEA;QACA,MAAMG,UAAU,GAAGN,iBAAiB,CAACI,SAAS,CAAC;QAC/CnQ,oBAAoB,CAACmQ,SAAS,CAAC;QAC/BjQ,iBAAiB,CAACmQ,UAAU,CAACpQ,cAAc,CAAC;QAC5CG,UAAU,CAACiQ,UAAU,CAAChG,IAAI,CAAC;QAE3B,OAAO8F,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACAtS,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIgF,iBAAiB,CAACsF,OAAO,EAAE;QAC7BiI,aAAa,CAACvN,iBAAiB,CAACsF,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtK,SAAS,CAAC,MAAM;IACd,IAAI4C,YAAY,IAAIM,kBAAkB,KAAK,SAAS,EAAE;MACpD;MACAwF,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC9F,YAAY,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,IAAIyS,iBAAiB,GAAG,IAAI;IAE5B,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,IAAI7L,SAAS,CAACC,WAAW,IAAID,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;UACxD,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UAE7EwL,iBAAiB,GAAGA,CAAA,KAAM;YACxBtP,qBAAqB,CAAC6D,UAAU,CAACE,KAAK,CAAC;YACvC,IAAIF,UAAU,CAACE,KAAK,KAAK,SAAS,IAAItE,YAAY,IAAIE,WAAW,KAAK,eAAe,EAAE;cACrF4F,qBAAqB,CAAC,CAAC;YACzB;UACF,CAAC;UAED1B,UAAU,CAAC2L,gBAAgB,CAAC,QAAQ,EAAEF,iBAAiB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAOtL,GAAG,EAAE;QACZC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,GAAG,CAAC;MACzD;IACF,CAAC;IAEDuL,gBAAgB,CAAC,CAAC;IAElB,OAAO,MAAM;MACX,IAAID,iBAAiB,EAAE;QACrB,IAAI;UACF,MAAMzL,UAAU,GAAGH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UACvED,UAAU,CAAC4L,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,mBAAmB,CAAC,QAAQ,EAAEL,iBAAiB,CAAC,CAAC;QAC1E,CAAC,CAAC,OAAOtL,GAAG,EAAE;UACZC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,GAAG,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACvE,YAAY,EAAEE,WAAW,CAAC,CAAC;;EAE/B;EACA9C,SAAS,CAAC,MAAM;IACd;IACA;EAAA,CACD,EAAE,CAACkC,iBAAiB,EAAEF,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAM+Q,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI/N,iBAAiB,CAACsF,OAAO,EAAE;MAC7BiI,aAAa,CAACvN,iBAAiB,CAACsF,OAAO,CAAC;MACxCzF,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,oBACE1D,OAAA,CAAClB,SAAS;IAAC+S,SAAS,EAAC,eAAe;IAAA9M,QAAA,gBAElC/E,OAAA,CAACd,IAAI;MACH4S,SAAS,EAAEzR,SAAU;MACrB0R,QAAQ,EAAGC,CAAC,IAAK1R,YAAY,CAAC0R,CAAC,CAAE;MACjCH,SAAS,EAAC,MAAM;MAAA9M,QAAA,gBAEhB/E,OAAA,CAACb,GAAG;QAAC8S,QAAQ,EAAC,WAAW;QAACC,KAAK,EAAC,iBAAiB;QAAAnN,QAAA,gBAC/C/E,OAAA,CAACjB,IAAI;UAAC8S,SAAS,EAAC,MAAM;UAAA9M,QAAA,eACpB/E,OAAA,CAACjB,IAAI,CAACuG,IAAI;YAACuM,SAAS,EAAC,MAAM;YAAA9M,QAAA,gBACzB/E,OAAA,CAACf,IAAI,CAACkT,KAAK;cAACN,SAAS,EAAC,MAAM;cAAA9M,QAAA,gBAC1B/E,OAAA,CAACf,IAAI,CAACmT,KAAK;gBAACP,SAAS,EAAC,MAAM;gBAAA9M,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxDrF,OAAA,CAACf,IAAI,CAACoT,MAAM;gBACV/F,KAAK,EAAE/L,aAAc;gBACrB+R,QAAQ,EAAGnK,CAAC,IAAK3H,gBAAgB,CAAC2H,CAAC,CAACI,MAAM,CAAC+D,KAAK,CAAE;gBAAAvH,QAAA,gBAElD/E,OAAA;kBAAQsM,KAAK,EAAC,KAAK;kBAAAvH,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DrF,OAAA;kBAAQsM,KAAK,EAAC,UAAU;kBAAAvH,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CrF,OAAA;kBAAQsM,KAAK,EAAC,QAAQ;kBAAAvH,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDrF,OAAA;kBAAQsM,KAAK,EAAC,OAAO;kBAAAvH,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGbrF,OAAA;cAAK6R,SAAS,EAAC,qCAAqC;cAAA9M,QAAA,gBAClD/E,OAAA,CAACV,cAAc;gBACbiT,OAAO,EAAC,OAAO;gBACfC,SAAS,EAAC,OAAO;gBACjBC,OAAO,EAAE9N,eAAgB;gBACzB+N,SAAS;gBAAA3N,QAAA,eAET/E,OAAA;kBACE6R,SAAS,EAAC,kBAAkB;kBAC5BhN,KAAK,EAAE;oBAAE8N,MAAM,EAAE,SAAS;oBAAEC,OAAO,EAAE;kBAAe,CAAE;kBAAA7N,QAAA,eAEtD/E,OAAA;oBACE6S,GAAG,EAAC,mBAAmB;oBACvBC,GAAG,EAAC,yBAAyB;oBAC7BjO,KAAK,EAAE;sBAAEkO,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE;oBAAO;kBAAE;oBAAA9N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGjBrF,OAAA;gBAAK6R,SAAS,EAAC,6BAA6B;gBAAA9M,QAAA,gBAC1C/E,OAAA;kBAAK6R,SAAS,EAAC,wDAAwD;kBAAA9M,QAAA,gBACrE/E,OAAA;oBAAM6R,SAAS,EAAC,MAAM;oBAAChN,KAAK,EAAE;sBAAEoO,QAAQ,EAAE,QAAQ;sBAAEC,UAAU,EAAE,KAAK;sBAAEC,KAAK,EAAE;oBAAU,CAAE;oBAAApO,QAAA,EAAC;kBAE3F;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACLrF,OAAA,CAACV,cAAc;oBACbkT,SAAS,EAAC,OAAO;oBACjBY,KAAK,EAAE;sBAAEC,IAAI,EAAE,GAAG;sBAAEC,IAAI,EAAE;oBAAI,CAAE;oBAChCb,OAAO,eACLzS,OAAA,CAACT,OAAO;sBAACqF,EAAE,EAAC,mCAAmC;sBAACC,KAAK,EAAE;wBAAEC,QAAQ,EAAE;sBAAQ,CAAE;sBAAAC,QAAA,gBAC3E/E,OAAA,CAACT,OAAO,CAACyF,MAAM;wBAACC,EAAE,EAAC,IAAI;wBAAAF,QAAA,gBACrB/E,OAAA;0BAAG6R,SAAS,EAAC;wBAAgC;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,+BAEpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAgB,CAAC,eACjBrF,OAAA,CAACT,OAAO,CAAC+F,IAAI;wBAAAP,QAAA,gBACX/E,OAAA;0BAAK6R,SAAS,EAAC,MAAM;0BAAA9M,QAAA,gBACnB/E,OAAA;4BAAK6R,SAAS,EAAC,MAAM;4BAAA9M,QAAA,gBACnB/E,OAAA;8BAAG6R,SAAS,EAAC;4BAAoC;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACtDrF,OAAA;8BAAA+E,QAAA,EAAQ;4BAAa;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CAAC,eACNrF,OAAA;4BAAK6E,KAAK,EAAE;8BAAEoO,QAAQ,EAAE,MAAM;8BAAEE,KAAK,EAAE,SAAS;8BAAEI,UAAU,EAAE;4BAAO,CAAE;4BAAAxO,QAAA,GAAC,kDAC3B,eAAA/E,OAAA;8BAAAkF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,gDACT,eAAArF,OAAA;8BAAAkF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,iDAE9C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENrF,OAAA;0BAAK6R,SAAS,EAAC,MAAM;0BAAA9M,QAAA,gBACnB/E,OAAA;4BAAK6R,SAAS,EAAC,MAAM;4BAAA9M,QAAA,gBACnB/E,OAAA;8BAAG6R,SAAS,EAAC;4BAAuC;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzDrF,OAAA;8BAAA+E,QAAA,EAAQ;4BAAe;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B,CAAC,eACNrF,OAAA;4BAAK6E,KAAK,EAAE;8BAAEoO,QAAQ,EAAE,MAAM;8BAAEE,KAAK,EAAE,SAAS;8BAAEI,UAAU,EAAE;4BAAO,CAAE;4BAAAxO,QAAA,GAAC,sCACvC,eAAA/E,OAAA;8BAAAkF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,oCACT,eAAArF,OAAA;8BAAAkF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,sDAElC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENrF,OAAA;0BAAK6R,SAAS,EAAC,iCAAiC;0BAAChN,KAAK,EAAE;4BAAEoO,QAAQ,EAAE;0BAAO,CAAE;0BAAAlO,QAAA,gBAC3E/E,OAAA;4BAAG6R,SAAS,EAAC;0BAAuB;4BAAA3M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACzCrF,OAAA;4BAAA+E,QAAA,EAAQ;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,0GAElC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACV;oBAAAN,QAAA,eAED/E,OAAA;sBAAM6R,SAAS,EAAC,mBAAmB;sBAAA9M,QAAA,eACjC/E,OAAA;wBAAM6R,SAAS,EAAC,+BAA+B;wBAC5ChN,KAAK,EAAE;0BACLoO,QAAQ,EAAE,MAAM;0BAChBN,MAAM,EAAE,MAAM;0BACdQ,KAAK,EAAE,SAAS;0BAChBP,OAAO,EAAE,aAAa;0BACtBY,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBzM,QAAQ,EAAE,UAAU;0BACpB0M,MAAM,EAAE,MAAM;0BACdR,UAAU,EAAE;wBACd,CAAE;wBAAAnO,QAAA,EACJ;sBAAC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACNrF,OAAA;kBAAK6R,SAAS,EAAC,2BAA2B;kBAAA9M,QAAA,gBACxC/E,OAAA;oBACE6R,SAAS,EAAC,oBAAoB;oBAC9B8B,OAAO,EAAEA,CAAA,KAAM5P,4BAA4B,CAAC,CAACD,yBAAyB,CAAE;oBACxEe,KAAK,EAAE;sBACLkO,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdY,eAAe,EAAE9P,yBAAyB,GAAG,SAAS,GAAG,SAAS;sBAClE+P,YAAY,EAAE,MAAM;sBACpB7M,QAAQ,EAAE,UAAU;sBACpB2L,MAAM,EAAE,SAAS;sBACjBmB,UAAU,EAAE,4BAA4B;sBACxCC,MAAM,EAAE;oBACV,CAAE;oBAAAhP,QAAA,gBAEF/E,OAAA;sBACE6R,SAAS,EAAC,eAAe;sBACzBhN,KAAK,EAAE;wBACLkO,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACdY,eAAe,EAAE,OAAO;wBACxBC,YAAY,EAAE,KAAK;wBACnB7M,QAAQ,EAAE,UAAU;wBACpBgN,GAAG,EAAE,KAAK;wBACVC,IAAI,EAAEnQ,yBAAyB,GAAG,MAAM,GAAG,KAAK;wBAChDgQ,UAAU,EAAE,gBAAgB;wBAC5BI,SAAS,EAAE;sBACb;oBAAE;sBAAAhP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFrF,OAAA;sBACE6E,KAAK,EAAE;wBACLmC,QAAQ,EAAE,UAAU;wBACpBgN,GAAG,EAAE,KAAK;wBACVC,IAAI,EAAEnQ,yBAAyB,GAAG,KAAK,GAAG,MAAM;wBAChDqQ,SAAS,EAAE,kBAAkB;wBAC7BlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,KAAK;wBACjBC,KAAK,EAAE,OAAO;wBACdW,UAAU,EAAE,eAAe;wBAC3BM,UAAU,EAAE;sBACd,CAAE;sBAAArP,QAAA,EAEDjB,yBAAyB,GAAG,IAAI,GAAG;oBAAK;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNrF,OAAA;oBAAO6R,SAAS,EAAC,YAAY;oBAAChN,KAAK,EAAE;sBAAEoO,QAAQ,EAAE;oBAAO,CAAE;oBAAAlO,QAAA,EACvDjB,yBAAyB,GAAG,4BAA4B,GAAG;kBAAsB;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGH,CAAC,eAERrF,OAAA;cAAK6R,SAAS,EAAC,MAAM;cAAA9M,QAAA,gBACnB/E,OAAA,CAACf,IAAI,CAACmT,KAAK;gBAAArN,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCrF,OAAA;gBAAK6R,SAAS,EAAC,mBAAmB;gBAAA9M,QAAA,gBAChC/E,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAE5S,YAAY,GAAG,SAAS,GAAG,iBAAkB;kBACtDkS,OAAO,EAAE7J,YAAa;kBACtBwK,QAAQ,EAAEnS,eAAgB;kBAAA4C,QAAA,EAEzB5C,eAAe,gBACdnC,OAAA,CAAAE,SAAA;oBAAA6E,QAAA,gBACE/E,OAAA,CAACX,OAAO;sBAAC4F,EAAE,EAAC,MAAM;sBAACsP,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAACxJ,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnFrF,OAAA;sBAAM6R,SAAS,EAAC,MAAM;sBAAA9M,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACjD,CAAC,GAEH5D,YAAY,GAAG,gBAAgB,GAAG;gBACnC;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACTrF,OAAA;kBAAK6R,SAAS,EAAC,sBAAsB;kBAAA9M,QAAA,eACnC/E,OAAA;oBAAO6R,SAAS,EAAC,kBAAkB;oBAAA9M,QAAA,GAAC,cAElC,eAAA/E,OAAA;sBACEyO,IAAI,EAAC,MAAM;sBACXoD,SAAS,EAAC,YAAY;sBACtB4C,MAAM,EAAC,SAAS;sBAChBnC,QAAQ,EAAEpK,gBAAiB;sBAC3BwM,GAAG,EAAEjQ,YAAa;sBAClB6P,QAAQ,EAAE7S,YAAa;sBACvBkT,QAAQ;oBAAA;sBAAAzP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL5D,YAAY,iBACXzB,OAAA;gBAAK6R,SAAS,EAAC,sBAAsB;gBAAA9M,QAAA,gBACnC/E,OAAA;kBAAO6R,SAAS,EAAC,YAAY;kBAAA9M,QAAA,gBAC3B/E,OAAA;oBAAA+E,QAAA,EAAQ;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChCtD,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAM6R,SAAS,EAAC,mBAAmB;oBAAA9M,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACxFtD,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAM6R,SAAS,EAAC,kBAAkB;oBAAA9M,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACrFtD,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAM6R,SAAS,EAAC,mBAAmB;oBAAA9M,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC7FtD,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAM6R,SAAS,EAAC,qBAAqB;oBAAA9M,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,EACP,CAAC1D,WAAW,KAAK,eAAe,IAAIgI,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,GAAG,CAAC,kBAC3ExI,OAAA;kBAAK6R,SAAS,EAAC,MAAM;kBAAA9M,QAAA,gBACnB/E,OAAA;oBAAO6R,SAAS,EAAC,YAAY;oBAAA9M,QAAA,gBAC3B/E,OAAA;sBAAA+E,QAAA,EAAQ;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAArF,OAAA;sBAAM6R,SAAS,EAAC,cAAc;sBAAA9M,QAAA,EAAEpD;oBAAW;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,EACPsE,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,GAAG,CAAC,iBACvCxI,OAAA;oBAAK6R,SAAS,EAAC,MAAM;oBAAA9M,QAAA,eACnB/E,OAAA;sBAAO6R,SAAS,EAAC,YAAY;sBAAA9M,QAAA,gBAC3B/E,OAAA;wBAAA+E,QAAA,EAAQ;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,eAAArF,OAAA;wBAAM6R,SAAS,EAAC,cAAc;wBAAA9M,QAAA,EAAE2E,uBAAuB,CAAC;sBAAC;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EACApD,aAAa,iBACZjC,OAAA,CAACZ,KAAK;kBAACiV,OAAO,EAAC,SAAS;kBAACxC,SAAS,EAAC,WAAW;kBAAChN,KAAK,EAAE;oBAAEoO,QAAQ,EAAE;kBAAW,CAAE;kBAAAlO,QAAA,gBAC7E/E,OAAA,CAACZ,KAAK,CAACwV,OAAO;oBAAC3P,EAAE,EAAC,IAAI;oBAAAF,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eAC5DrF,OAAA;oBAAK6E,KAAK,EAAE;sBAAEgQ,UAAU,EAAE;oBAAW,CAAE;oBAAA9P,QAAA,EAAE9C;kBAAa;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DrF,OAAA;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrF,OAAA;oBAAK6R,SAAS,EAAC,4BAA4B;oBAAA9M,QAAA,eACzC/E,OAAA,CAAChB,MAAM;sBAACqV,OAAO,EAAC,iBAAiB;sBAACG,IAAI,EAAC,IAAI;sBAACb,OAAO,EAAEpM,qBAAsB;sBAAAxC,QAAA,EAAC;oBAE5E;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL5D,YAAY,iBACXzB,OAAA;cAAK6R,SAAS,EAAC,uBAAuB;cAAA9M,QAAA,gBACpC/E,OAAA,CAACN,MAAM;gBACLoV,KAAK,EAAE,KAAM;gBACbJ,GAAG,EAAElQ,SAAU;gBACfuQ,gBAAgB,EAAC,YAAY;gBAC7BlD,SAAS,EAAC,QAAQ;gBAClBmD,gBAAgB,EAAE;kBAChBjC,KAAK,EAAE,GAAG;kBACVC,MAAM,EAAE,GAAG;kBACXiC,UAAU,EAAEpT;gBACd;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDX,QAAQ,iBACP1E,OAAA,CAAChB,MAAM;gBACLqV,OAAO,EAAC,mBAAmB;gBAC3BV,OAAO,EAAE3J,uBAAwB;gBACjC6H,SAAS,EAAC,WAAW;gBACrB2C,IAAI,EAAC,IAAI;gBAAAzP,QAAA,EACV;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eACDrF,OAAA,CAAChB,MAAM;gBACLqV,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAE1K,aAAc;gBACvB4I,SAAS,EAAC,MAAM;gBAAA9M,QAAA,EACjB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEAsE,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,GAAG,CAAC,iBACvCxI,OAAA;cAAK6R,SAAS,EAAC,8BAA8B;cAAA9M,QAAA,gBAC3C/E,OAAA;gBAAA+E,QAAA,EAAI;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBrF,OAAA;gBAAK6R,SAAS,EAAC,eAAe;gBAAA9M,QAAA,EAC3B4E,MAAM,CAACuL,OAAO,CAACvU,gBAAgB,CAAC,CAACkP,GAAG,CAAC,CAAC,CAAC/J,IAAI,EAAEqP,OAAO,CAAC,EAAEC,KAAK,kBAC3DpV,OAAA;kBAEE6R,SAAS,EAAE,mBAAmBuD,KAAK,KAAKrU,iBAAiB,GAAG,UAAU,GAAG,EAAE,EAAG;kBAC9E4S,OAAO,EAAEA,CAAA,KAAM3S,oBAAoB,CAACoU,KAAK,CAAE;kBAAArQ,QAAA,gBAE3C/E,OAAA;oBACE6S,GAAG,EAAEsC,OAAQ;oBACbrC,GAAG,EAAE,WAAWsC,KAAK,GAAG,CAAC,EAAG;oBAC5BvD,SAAS,EAAC;kBAAe;oBAAA3M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACFrF,OAAA;oBACE6R,SAAS,EAAC,oCAAoC;oBAC9C8B,OAAO,EAAGxL,CAAC,IAAK;sBACdA,CAAC,CAACkN,eAAe,CAAC,CAAC;sBACnB,MAAMC,QAAQ,GAAG7U,UAAU,CAAC0M,MAAM,CAAC,CAACoI,CAAC,EAAE5I,CAAC,KAAKA,CAAC,KAAKyI,KAAK,CAAC;sBACzD,MAAMI,cAAc,GAAG;wBAAC,GAAG7U;sBAAgB,CAAC;sBAC5C,MAAM8U,cAAc,GAAG;wBAAC,GAAG5U;sBAAgB,CAAC;sBAC5C,OAAO2U,cAAc,CAAC1P,IAAI,CAAC;sBAC3B,OAAO2P,cAAc,CAAC3P,IAAI,CAAC;sBAC3BpF,aAAa,CAAC4U,QAAQ,CAAC;sBACvB1U,mBAAmB,CAAC4U,cAAc,CAAC;sBACnC1U,mBAAmB,CAAC2U,cAAc,CAAC;sBACnC,IAAI1U,iBAAiB,IAAIuU,QAAQ,CAAC9M,MAAM,EAAE;wBACxCxH,oBAAoB,CAAC0U,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAAC9M,MAAM,GAAG,CAAC,CAAC,CAAC;sBACxD;oBACF,CAAE;oBAAAzD,QAAA,EACH;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GA3BJS,IAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BN,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrF,OAAA;gBAAK6R,SAAS,EAAC,uBAAuB;gBAAA9M,QAAA,EACnC4E,MAAM,CAACe,MAAM,CAAC/J,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,iBACjDf,OAAA;kBACE6S,GAAG,EAAElJ,MAAM,CAACe,MAAM,CAAC/J,gBAAgB,CAAC,CAACI,iBAAiB,CAAE;kBACxD+R,GAAG,EAAC,iBAAiB;kBACrBjB,SAAS,EAAC;gBAAyB;kBAAA3M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEA9D,KAAK,iBAAIvB,OAAA,CAACZ,KAAK;cAACiV,OAAO,EAAC,QAAQ;cAAAtP,QAAA,EAAExD;YAAK;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEjDrF,OAAA;cAAK6R,SAAS,EAAC,mBAAmB;cAAA9M,QAAA,gBAChC/E,OAAA,CAAChB,MAAM;gBACLqV,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAEzJ,aAAc;gBACvBoK,QAAQ,EAAE3K,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,KAAK,CAAC,IAAInH,OAAO,IAAIkB,eAAgB;gBAAAwC,QAAA,EAElF1D,OAAO,gBACNrB,OAAA,CAAAE,SAAA;kBAAA6E,QAAA,gBACE/E,OAAA,CAACX,OAAO;oBAAC4F,EAAE,EAAC,MAAM;oBAACsP,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAACxJ,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnFrF,OAAA;oBAAM6R,SAAS,EAAC,MAAM;oBAAA9M,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC1C,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETrF,OAAA,CAAChB,MAAM;gBACLqV,OAAO,EAAC,SAAS;gBACjBV,OAAO,EAAEjH,gBAAiB;gBAC1B4H,QAAQ,EAAE3K,MAAM,CAACC,IAAI,CAACjJ,gBAAgB,CAAC,CAAC6H,MAAM,KAAK,CAAC,IAAInH,OAAO,IAAIkB,eAAgB;gBAAAwC,QAAA,EAElFxC,eAAe,gBACdvC,OAAA,CAAAE,SAAA;kBAAA6E,QAAA,gBACE/E,OAAA,CAACX,OAAO;oBAAC4F,EAAE,EAAC,MAAM;oBAACsP,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAACxJ,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnFrF,OAAA;oBAAM6R,SAAS,EAAC,MAAM;oBAAA9M,QAAA,GAAC,aAAW,EAACtC,cAAc,EAAC,GAAC,EAACQ,cAAc;kBAAA;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,eAC1E,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETrF,OAAA,CAAChB,MAAM;gBACLqV,OAAO,EAAC,WAAW;gBACnBV,OAAO,EAAEpG,WAAY;gBACrB+G,QAAQ,EAAEjT,OAAO,IAAIkB,eAAgB;gBAAAwC,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAINhD,YAAY,CAACuT,IAAI,CAAC7M,MAAM;UAAA,IAAA8M,qBAAA;UAAA,OAAI9M,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI,EAAAoK,qBAAA,GAAA9M,MAAM,CAACoD,eAAe,cAAA0J,qBAAA,uBAAtBA,qBAAA,CAAwBzJ,KAAK,IAAG,CAAC;QAAA,EAAC,iBACnGpM,OAAA,CAACjB,IAAI;UAAC8S,SAAS,EAAC,MAAM;UAAA9M,QAAA,gBACpB/E,OAAA,CAACjB,IAAI,CAACiG,MAAM;YAAAD,QAAA,eACV/E,OAAA;cAAK6R,SAAS,EAAC,mDAAmD;cAAA9M,QAAA,gBAChE/E,OAAA;gBAAI6R,SAAS,EAAC,MAAM;gBAAA9M,QAAA,gBAClB/E,OAAA;kBAAG6R,SAAS,EAAC;gBAAmB;kBAAA3M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,8BAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrF,OAAA;gBAAK6R,SAAS,EAAC,cAAc;gBAAA9M,QAAA,gBAC3B/E,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAEnQ,mBAAmB,GAAG,SAAS,GAAG,iBAAkB;kBAC7DsQ,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAMxP,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAAAa,QAAA,EAE3Db,mBAAmB,GAAG,cAAc,GAAG;gBAAc;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACTrF,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAC,SAAS;kBACjBG,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAErF,WAAY;kBACrB4D,KAAK,EAAC,uBAAuB;kBAAAnN,QAAA,gBAE7B/E,OAAA;oBAAG6R,SAAS,EAAC;kBAAsB;oBAAA3M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,cAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,EACbnB,mBAAmB,iBAClBlE,OAAA,CAACjB,IAAI,CAACuG,IAAI;YAAAP,QAAA,gBAER/E,OAAA;cAAK6R,SAAS,EAAC,MAAM;cAAA9M,QAAA,eACnB/E,OAAA;gBAAK6R,SAAS,EAAC,wBAAwB;gBAAA9M,QAAA,gBACrC/E,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAErQ,oBAAoB,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;kBACxEwQ,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM1P,uBAAuB,CAAC,KAAK,CAAE;kBAAAc,QAAA,EAC/C;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrF,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAErQ,oBAAoB,KAAK,UAAU,GAAG,QAAQ,GAAG,gBAAiB;kBAC3EwQ,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM1P,uBAAuB,CAAC,UAAU,CAAE;kBAAAc,QAAA,EACpD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrF,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAErQ,oBAAoB,KAAK,QAAQ,GAAG,SAAS,GAAG,iBAAkB;kBAC3EwQ,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM1P,uBAAuB,CAAC,QAAQ,CAAE;kBAAAc,QAAA,EAClD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrF,OAAA,CAAChB,MAAM;kBACLqV,OAAO,EAAErQ,oBAAoB,KAAK,OAAO,GAAG,MAAM,GAAG,cAAe;kBACpEwQ,IAAI,EAAC,IAAI;kBACTb,OAAO,EAAEA,CAAA,KAAM1P,uBAAuB,CAAC,OAAO,CAAE;kBAAAc,QAAA,EACjD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrF,OAAA;cAAK6R,SAAS,EAAC,8BAA8B;cAAA9M,QAAA,eAC3C/E,OAAA;gBAAK6R,SAAS,EAAC,KAAK;gBAAA9M,QAAA,gBAClB/E,OAAA;kBAAK6R,SAAS,EAAC,UAAU;kBAAA9M,QAAA,eACvB/E,OAAA;oBAAK6R,SAAS,EAAC,2BAA2B;oBAAA9M,QAAA,eACxC/E,OAAA;sBAAK6R,SAAS,EAAC,4BAA4B;sBAAA9M,QAAA,gBACzC/E,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxCrF,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EACjB1C,YAAY,CAACyT,MAAM,CAAC,CAACC,GAAG,EAAEhN,MAAM;0BAAA,IAAAiN,sBAAA;0BAAA,OAAKD,GAAG,IAAI,EAAAC,sBAAA,GAAAjN,MAAM,CAACoD,eAAe,cAAA6J,sBAAA,uBAAtBA,sBAAA,CAAwBjK,QAAQ,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA7G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrF,OAAA;kBAAK6R,SAAS,EAAC,UAAU;kBAAA9M,QAAA,eACvB/E,OAAA;oBAAK6R,SAAS,EAAC,4BAA4B;oBAAA9M,QAAA,eACzC/E,OAAA;sBAAK6R,SAAS,EAAC,4BAA4B;sBAAA9M,QAAA,gBACzC/E,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EAAC;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtCrF,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EACjB1C,YAAY,CAACyT,MAAM,CAAC,CAACC,GAAG,EAAEhN,MAAM;0BAAA,IAAAkN,sBAAA;0BAAA,OAAKF,GAAG,IAAI,EAAAE,sBAAA,GAAAlN,MAAM,CAACoD,eAAe,cAAA8J,sBAAA,uBAAtBA,sBAAA,CAAwBjK,MAAM,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA9G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrF,OAAA;kBAAK6R,SAAS,EAAC,UAAU;kBAAA9M,QAAA,eACvB/E,OAAA;oBAAK6R,SAAS,EAAC,yBAAyB;oBAAA9M,QAAA,eACtC/E,OAAA;sBAAK6R,SAAS,EAAC,4BAA4B;sBAAA9M,QAAA,gBACzC/E,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EAAC;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrCrF,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EACjB1C,YAAY,CAACyT,MAAM,CAAC,CAACC,GAAG,EAAEhN,MAAM;0BAAA,IAAAmN,sBAAA;0BAAA,OAAKH,GAAG,IAAI,EAAAG,sBAAA,GAAAnN,MAAM,CAACoD,eAAe,cAAA+J,sBAAA,uBAAtBA,sBAAA,CAAwBjK,KAAK,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAA/G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrF,OAAA;kBAAK6R,SAAS,EAAC,UAAU;kBAAA9M,QAAA,eACvB/E,OAAA;oBAAK6R,SAAS,EAAC,4BAA4B;oBAAA9M,QAAA,eACzC/E,OAAA;sBAAK6R,SAAS,EAAC,4BAA4B;sBAAA9M,QAAA,gBACzC/E,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EAAC;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1CrF,OAAA;wBAAI6R,SAAS,EAAC,MAAM;wBAAA9M,QAAA,EACjB1C,YAAY,CAACyT,MAAM,CAAC,CAACC,GAAG,EAAEhN,MAAM;0BAAA,IAAAoN,sBAAA;0BAAA,OAAKJ,GAAG,IAAI,EAAAI,sBAAA,GAAApN,MAAM,CAACoD,eAAe,cAAAgK,sBAAA,uBAAtBA,sBAAA,CAAwB/J,KAAK,KAAI,CAAC,CAAC;wBAAA,GAAE,CAAC;sBAAC;wBAAAlH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrF,OAAA;cAAK6R,SAAS,EAAC,4CAA4C;cAAA9M,QAAA,EACxD,CAAC,MAAM;gBACN;gBACA,MAAMwJ,aAAa,GAAG,EAAE;gBAExBlM,YAAY,CAACoG,OAAO,CAACM,MAAM,IAAI;kBAC7B,IAAIA,MAAM,CAACuC,OAAO,IAAIvC,MAAM,CAAC0C,SAAS,IAAI1C,MAAM,CAAC+C,gBAAgB,EAAE;oBACjE,MAAM;sBAAEC,QAAQ;sBAAEC,MAAM;sBAAEC;oBAAM,CAAC,GAAGlD,MAAM,CAAC+C,gBAAgB;;oBAE3D;oBACA,IAAI9H,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,EAAE;sBACzE+H,QAAQ,CAACtD,OAAO,CAAC+F,OAAO,IAAI;wBAC1BD,aAAa,CAACzB,IAAI,CAAC;0BACjB,GAAG0B,OAAO;0BACVC,IAAI,EAAE,SAAS;0BACfjF,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzBjJ,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIyD,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,EAAE;sBACvEgI,MAAM,CAACvD,OAAO,CAAC4G,KAAK,IAAI;wBACtBd,aAAa,CAACzB,IAAI,CAAC;0BACjB,GAAGuC,KAAK;0BACRZ,IAAI,EAAE,OAAO;0BACbjF,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzBjJ,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIyD,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,EAAE;sBACtEiI,KAAK,CAACxD,OAAO,CAAC8G,IAAI,IAAI;wBACpBhB,aAAa,CAACzB,IAAI,CAAC;0BACjB,GAAGyC,IAAI;0BACPd,IAAI,EAAE,MAAM;0BACZjF,QAAQ,EAAET,MAAM,CAACS,QAAQ;0BACzBjJ,aAAa,EAAE;wBACjB,CAAC,CAAC;sBACJ,CAAC,CAAC;oBACJ;kBACF;gBACF,CAAC,CAAC;gBAEF,IAAIgO,aAAa,CAAC/F,MAAM,KAAK,CAAC,EAAE;kBAC9B,oBACExI,OAAA;oBAAK6R,SAAS,EAAC,kBAAkB;oBAAA9M,QAAA,eAC/B/E,OAAA;sBAAG6R,SAAS,EAAC,YAAY;sBAAA9M,QAAA,EAAC;oBAA4C;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAEV;gBAEA,oBACErF,OAAA;kBAAO6R,SAAS,EAAC,oCAAoC;kBAAA9M,QAAA,gBACnD/E,OAAA;oBAAA+E,QAAA,eACE/E,OAAA;sBAAA+E,QAAA,gBACE/E,OAAA;wBAAA+E,QAAA,EAAI;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvBrF,OAAA;wBAAA+E,QAAA,EAAI;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxBrF,OAAA;wBACE6E,KAAK,EAAE;0BAAE8N,MAAM,EAAE;wBAAU,CAAE;wBAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,eAAe,CAAE;wBAAA9I,QAAA,GAC5C,OACM,EAACX,UAAU,CAACE,GAAG,KAAK,eAAe,iBACtCtE,OAAA;0BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;wBAAQ;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACxF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACLrF,OAAA;wBAAA+E,QAAA,EAAI;sBAAE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACV,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,kBACrEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,UAAU,CAAE;0BAAA9I,QAAA,GACvC,gBACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,UAAU,CAAE;0BAAA9I,QAAA,GACvC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,QAAQ,CAAE;0BAAA9I,QAAA,GACrC,kBACc,EAACX,UAAU,CAACE,GAAG,KAAK,QAAQ,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BAAA+E,QAAA,EAAI;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eACrB,CACH,EACA,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,kBACnEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,YAAY,CAAE;0BAAA9I,QAAA,GACzC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,YAAY,iBACzCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,UAAU,CAAE;0BAAA9I,QAAA,GACvC,gBACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BAAA+E,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eACnB,CACH,EACA,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,kBAClEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,WAAW,CAAE;0BAAA9I,QAAA,GACxC,YACW,EAACX,UAAU,CAACE,GAAG,KAAK,WAAW,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,WAAW,CAAE;0BAAA9I,QAAA,GACxC,YACW,EAACX,UAAU,CAACE,GAAG,KAAK,WAAW,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACLrF,OAAA;0BACE6E,KAAK,EAAE;4BAAE8N,MAAM,EAAE;0BAAU,CAAE;0BAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,UAAU,CAAE;0BAAA9I,QAAA,GACvC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,UAAU,iBACvCtE,OAAA;4BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;0BAAQ;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CACxF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA,eACL,CACH,eACDrF,OAAA;wBACE6E,KAAK,EAAE;0BAAE8N,MAAM,EAAE;wBAAU,CAAE;wBAC7BgB,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC,YAAY,CAAE;wBAAA9I,QAAA,GACzC,aACY,EAACX,UAAU,CAACE,GAAG,KAAK,YAAY,iBACzCtE,OAAA;0BAAG6R,SAAS,EAAE,eAAezN,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG,MAAM;wBAAQ;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACxF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRrF,OAAA;oBAAA+E,QAAA,EACG+I,cAAc,CAACS,aAAa,CAAC,CAACsB,GAAG,CAAC,CAACC,SAAS,EAAEsF,KAAK,kBAClDpV,OAAA;sBAAA+E,QAAA,gBAEE/E,OAAA;wBAAI6E,KAAK,EAAE;0BAAEkO,KAAK,EAAE,OAAO;0BAAEqD,SAAS,EAAE;wBAAS,CAAE;wBAAArR,QAAA,EAChD,CAAC,MAAM;0BACN,MAAMsH,aAAa,GAAG1L,gBAAgB,CAACmP,SAAS,CAACtG,QAAQ,CAAC;0BAC1D,OAAO6C,aAAa,gBAClBrM,OAAA;4BACE6R,SAAS,EAAC,2BAA2B;4BACrC8B,OAAO,EAAEA,CAAA;8BAAA,IAAA0C,kBAAA,EAAAC,mBAAA;8BAAA,OAAM1I,oBAAoB,CAAC;gCAClCvB,aAAa,EAAEA,aAAa;gCAC5BpL,cAAc,GAAAoV,kBAAA,GAAEhU,YAAY,CAACkU,IAAI,CAACnJ,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKsG,SAAS,CAACtG,QAAQ,CAAC,cAAA6M,kBAAA,uBAAzDA,kBAAA,CAA2DpV,cAAc;gCACzFyK,MAAM,GAAA4K,mBAAA,GAAEjU,YAAY,CAACkU,IAAI,CAACnJ,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKsG,SAAS,CAACtG,QAAQ,CAAC,cAAA8M,mBAAA,uBAAzDA,mBAAA,CAA2D5K,MAAM;gCACzElC,QAAQ,EAAEsG,SAAS,CAACtG;8BACtB,CAAC,CAAC;4BAAA,CAAC;4BACH0I,KAAK,EAAC,+BAAqB;4BAAAnN,QAAA,gBAE3B/E,OAAA;8BACE6S,GAAG,EAAExG,aAAc;8BACnByG,GAAG,EAAC,UAAU;8BACdjB,SAAS,EAAC;4BAAiB;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACFrF,OAAA;8BAAK6R,SAAS,EAAC,mBAAmB;8BAAA9M,QAAA,EAAC;4BAEnC;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAENrF,OAAA;4BAAO6R,SAAS,EAAC,YAAY;4BAAA9M,QAAA,EAAC;0BAAQ;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAC9C;wBACH,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAGLrF,OAAA;wBAAI6E,KAAK,EAAE;0BAAEkO,KAAK,EAAE,OAAO;0BAAEqD,SAAS,EAAE;wBAAS,CAAE;wBAAArR,QAAA,EAChD,CAAC,MAAM;0BACN,MAAMgE,MAAM,GAAG1G,YAAY,CAACkU,IAAI,CAACnJ,CAAC,IAAIA,CAAC,CAAC5D,QAAQ,KAAKsG,SAAS,CAACtG,QAAQ,CAAC;0BACxE,MAAMvI,cAAc,GAAG8H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE9H,cAAc;0BAC7C,MAAMoL,aAAa,GAAG1L,gBAAgB,CAACmP,SAAS,CAACtG,QAAQ,CAAC;0BAE1D,OAAOvI,cAAc,IAAI8H,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2C,MAAM,gBACrC1L,OAAA;4BACE6R,SAAS,EAAC,2BAA2B;4BACrC8B,OAAO,EAAEA,CAAA,KAAM/F,oBAAoB,CAAC;8BAClCvB,aAAa,EAAEA,aAAa;8BAC5BpL,cAAc,EAAEA,cAAc;8BAC9ByK,MAAM,EAAE3C,MAAM,CAAC2C,MAAM;8BACrBlC,QAAQ,EAAEsG,SAAS,CAACtG;4BACtB,CAAC,CAAE;4BACH0I,KAAK,EAAC,+BAAqB;4BAAAnN,QAAA,gBAE3B/E,OAAA;8BACE6S,GAAG,EAAE5R,cAAe;8BACpB6R,GAAG,EAAC,WAAW;8BACfjB,SAAS,EAAC;4BAAiB;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC,eACFrF,OAAA;8BAAK6R,SAAS,EAAC,mBAAmB;8BAAA9M,QAAA,EAAC;4BAEnC;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,gBAENrF,OAAA;4BAAO6R,SAAS,EAAC,YAAY;4BAAA9M,QAAA,EAAC;0BAAkB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CACxD;wBACH,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACLrF,OAAA;wBAAA+E,QAAA,eACE/E,OAAA;0BAAM6R,SAAS,EAAE,SACf/B,SAAS,CAACvP,aAAa,KAAK,UAAU,GAAG,WAAW,GACpDuP,SAAS,CAACvP,aAAa,KAAK,QAAQ,GAAG,YAAY,GAAG,SAAS,EAC9D;0BAAAwE,QAAA,EACA+K,SAAS,CAACrB;wBAAI;0BAAAvJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACLrF,OAAA;wBAAA+E,QAAA,EAAK+K,SAAS,CAACpB,UAAU,IAAIoB,SAAS,CAACR,QAAQ,IAAIQ,SAAS,CAACN,OAAO,IAAI4F,KAAK,GAAG;sBAAC;wBAAAlQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EAGtF,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,UAAU,kBACrEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACnB,QAAQ,GAAGmB,SAAS,CAACnB,QAAQ,CAAC7G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrErF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAAClB,QAAQ,GAAGkB,SAAS,CAAClB,QAAQ,CAAC9G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrErF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACjB,MAAM,GAAGiB,SAAS,CAACjB,MAAM,CAAC/G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjErF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAAChB,YAAY,IAAI;wBAAK;0BAAA5J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eAC1C,CACH,EAGA,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,QAAQ,kBACnEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACf,UAAU,IAAI;wBAAK;0BAAA7J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxCrF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACnB,QAAQ,GAAGmB,SAAS,CAACnB,QAAQ,CAAC7G,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrErF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACd,UAAU,IAAI;wBAAK;0BAAA9J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACxC,CACH,EAGA,CAACrB,oBAAoB,KAAK,KAAK,IAAIA,oBAAoB,KAAK,OAAO,kBAClEhE,OAAA,CAAAE,SAAA;wBAAA6E,QAAA,gBACE/E,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACb,SAAS,IAAI;wBAAK;0BAAA/J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACvCrF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACZ,SAAS,IAAI;wBAAK;0BAAAhK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACvCrF,OAAA;0BAAA+E,QAAA,EAAK+K,SAAS,CAACX,QAAQ,GAAGW,SAAS,CAACX,QAAQ,CAACrH,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,eACrE,CACH,eAEDrF,OAAA;wBAAA+E,QAAA,EAAK+K,SAAS,CAACV,UAAU,GAAG,CAACU,SAAS,CAACV,UAAU,GAAG,GAAG,EAAEtH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG;sBAAK;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GArGhF,GAAGyK,SAAS,CAACtG,QAAQ,IAAIsG,SAAS,CAACvP,aAAa,IAAI6U,KAAK,EAAE;sBAAAlQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAsGhE,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEZ,CAAC,EAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACP,EAGA9C,eAAe,iBACdvC,OAAA;UAAK6R,SAAS,EAAC,8BAA8B;UAAA9M,QAAA,eAC3C/E,OAAA;YAAK6R,SAAS,EAAC,2BAA2B;YAAA9M,QAAA,gBACxC/E,OAAA;cAAK6R,SAAS,EAAC,MAAM;cAAA9M,QAAA,eACnB/E,OAAA,CAACX,OAAO;gBAACkV,SAAS,EAAC,QAAQ;gBAACC,IAAI,EAAC,IAAI;gBAACxJ,IAAI,EAAC;cAAQ;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAI6R,SAAS,EAAC,MAAM;gBAAA9M,QAAA,GAAC,qBAAmB,EAACtC,cAAc,EAAC,GAAC,EAACQ,cAAc;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9ErF,OAAA;gBAAK6R,SAAS,EAAC,UAAU;gBAAChN,KAAK,EAAE;kBAAEmO,MAAM,EAAE;gBAAO,CAAE;gBAAAjO,QAAA,eAClD/E,OAAA;kBACE6R,SAAS,EAAC,cAAc;kBACxB7G,IAAI,EAAC,aAAa;kBAClBnG,KAAK,EAAE;oBAAEkO,KAAK,EAAE,GAAItQ,cAAc,GAAGQ,cAAc,GAAI,GAAG;kBAAI,CAAE;kBAChE,iBAAeR,cAAe;kBAC9B,iBAAc,GAAG;kBACjB,iBAAeQ;gBAAe;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAOA,CAAC9C,eAAe,IAAIF,YAAY,CAACmG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;UACrD,MAAMgO,WAAW,GAAGnU,YAAY,CAACmG,MAAM;UACvC,MAAMiO,gBAAgB,GAAGpU,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM;UACnE,MAAMkO,YAAY,GAAGrU,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM;UAEhE,IAAImO,YAAY,GAAG,OAAO;UAC1B,IAAIC,UAAU,GAAG,EAAE;UAEnB,IAAI9S,yBAAyB,EAAE;YAC7B;YACA,MAAM+S,aAAa,GAAGxU,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAAClD,MAAM;YAChE,MAAMsO,iBAAiB,GAAGN,WAAW,GAAG,CAAC,GAAIK,aAAa,GAAGL,WAAW,GAAI,GAAG,GAAG,CAAC;YAEnF,IAAIA,WAAW,GAAG,CAAC,EAAE;cACnB,IAAIM,iBAAiB,KAAK,CAAC,EAAE;gBAC3B;gBACAH,YAAY,GAAG,SAAS;cAC1B,CAAC,MAAM,IAAIG,iBAAiB,KAAK,GAAG,EAAE;gBACpC;gBACAH,YAAY,GAAG,QAAQ;cACzB,CAAC,MAAM;gBACL;gBACAA,YAAY,GAAG,SAAS;gBACxBC,UAAU,GAAG,sBAAsB;cACrC;YACF;UACF,CAAC,MAAM;YACL;YACA,IAAIF,YAAY,KAAK,CAAC,EAAE;cACtB;cACAC,YAAY,GAAG,SAAS;YAC1B,CAAC,MAAM,IAAIF,gBAAgB,KAAK,CAAC,EAAE;cACjC;cACAE,YAAY,GAAG,QAAQ;YACzB,CAAC,MAAM;cACL;cACAA,YAAY,GAAG,SAAS;YAC1B;UACF;UAEA,oBACE3W,OAAA;YAAK6R,SAAS,EAAC,4BAA4B;YAAA9M,QAAA,eACzC/E,OAAA,CAACZ,KAAK;cAACiV,OAAO,EAAEsC,YAAa;cAAC9E,SAAS,EAAE+E,UAAW;cAAA7R,QAAA,gBAClD/E,OAAA;gBAAG6R,SAAS,EAAC;cAA0B;gBAAA3M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,cAClC,EAAChD,YAAY,CAACmG,MAAM,EAAC,UAC/B,EAAC1E,yBAAyB,gBACxB9D,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,GACG1C,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI8B,CAAC,CAAC3B,SAAS,CAAC,CAACjD,MAAM,EAAC,yBAC3D,EAACnG,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,IAAI,CAAC8B,CAAC,CAAC3B,SAAS,CAAC,CAACjD,MAAM,EAAC,4BAC5D,EAACnG,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,UAC/C;cAAA,eAAE,CAAC,gBAEHxI,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,GACG1C,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,iCAC5C,EAACnG,YAAY,CAAC8K,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,OAAO,CAAC,CAAC9C,MAAM,EAAC,UAC/C;cAAA,eAAE,CACH;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAEV,CAAC,EAAE,CAAC,EAGH,CAAC9C,eAAe,IAAIF,YAAY,CAACmG,MAAM,GAAG,CAAC,IAAI1E,yBAAyB,iBACvE9D,OAAA;UAAK6R,SAAS,EAAC,yBAAyB;UAAA9M,QAAA,eACtC/E,OAAA,CAACjB,IAAI;YAAAgG,QAAA,gBACH/E,OAAA,CAACjB,IAAI,CAACiG,MAAM;cAAAD,QAAA,eACV/E,OAAA;gBAAK6R,SAAS,EAAC,mDAAmD;gBAAA9M,QAAA,gBAChE/E,OAAA;kBAAI6R,SAAS,EAAC,MAAM;kBAAA9M,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDrF,OAAA;kBAAK6R,SAAS,EAAC,gBAAgB;kBAAA9M,QAAA,gBAC7B/E,OAAA,CAAChB,MAAM;oBACLqV,OAAO,EAAE9Q,WAAW,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;oBAC/DiR,IAAI,EAAC,IAAI;oBACT3C,SAAS,EAAC,MAAM;oBAChB8B,OAAO,EAAEA,CAAA,KAAMnQ,cAAc,CAAC,KAAK,CAAE;oBAAAuB,QAAA,EACtC;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrF,OAAA,CAAChB,MAAM;oBACLqV,OAAO,EAAE9Q,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,iBAAkB;oBAChEiR,IAAI,EAAC,IAAI;oBACT3C,SAAS,EAAC,MAAM;oBAChB8B,OAAO,EAAEA,CAAA,KAAMnQ,cAAc,CAAC,MAAM,CAAE;oBAAAuB,QAAA,EACvC;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrF,OAAA,CAAChB,MAAM;oBACLqV,OAAO,EAAE9Q,WAAW,KAAK,UAAU,GAAG,QAAQ,GAAG,gBAAiB;oBAClEiR,IAAI,EAAC,IAAI;oBACTb,OAAO,EAAEA,CAAA,KAAMnQ,cAAc,CAAC,UAAU,CAAE;oBAAAuB,QAAA,EAC3C;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACdrF,OAAA,CAACjB,IAAI,CAACuG,IAAI;cAAAP,QAAA,eACR/E,OAAA;gBAAK6R,SAAS,EAAC,kBAAkB;gBAAA9M,QAAA,eAC/B/E,OAAA;kBAAO6R,SAAS,EAAC,qBAAqB;kBAAA9M,QAAA,gBACpC/E,OAAA;oBAAA+E,QAAA,eACE/E,OAAA;sBAAA+E,QAAA,gBACE/E,OAAA;wBAAA+E,QAAA,EAAI;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACdrF,OAAA;wBAAA+E,QAAA,EAAI;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRrF,OAAA;oBAAA+E,QAAA,EACG1C,YAAY,CACV8K,MAAM,CAACpE,MAAM,IAAI;sBAChB,IAAIxF,WAAW,KAAK,MAAM,EAAE,OAAOwF,MAAM,CAAC2C,MAAM;sBAChD,IAAInI,WAAW,KAAK,UAAU,EAAE,OAAO,CAACwF,MAAM,CAAC2C,MAAM;sBACrD,OAAO,IAAI,CAAC,CAAC;oBACf,CAAC,CAAC,CACDmE,GAAG,CAAC,CAAC9G,MAAM,EAAEqM,KAAK,KAAK;sBACtB,MAAM5L,QAAQ,GAAGT,MAAM,CAACS,QAAQ;sBAChC,MAAMkC,MAAM,GAAG3C,MAAM,CAAC2C,MAAM;;sBAE5B;sBACA,IAAIqL,YAAY,GAAG,IAAI;sBACvB,IAAInK,SAAS,GAAG,IAAI;sBAEpB,IAAIjK,mBAAmB,CAAC6G,QAAQ,CAAC,EAAE;wBACjC;wBACAuN,YAAY,GAAGpU,mBAAmB,CAAC6G,QAAQ,CAAC,CAAC6C,aAAa;wBAC1DO,SAAS,GAAGjK,mBAAmB,CAAC6G,QAAQ,CAAC;wBACzCvD,OAAO,CAAC8B,GAAG,CAAC,wBAAwB,EAAEyB,QAAQ,EAAE,WAAW,EAAE,CAAC,CAACuN,YAAY,CAAC;sBAC9E,CAAC,MAAM,IAAIpW,gBAAgB,CAAC6I,QAAQ,CAAC,EAAE;wBACrC;wBACAuN,YAAY,GAAGpW,gBAAgB,CAAC6I,QAAQ,CAAC;wBACzCoD,SAAS,GAAG;0BACVP,aAAa,EAAE0K,YAAY;0BAC3B9V,cAAc,EAAE,IAAI;0BACpBE,OAAO,EAAE,IAAI;0BACbuK,MAAM,EAAEA;wBACV,CAAC;wBACDzF,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,EAAEyB,QAAQ,EAAE,WAAW,EAAE,CAAC,CAACuN,YAAY,CAAC;sBAChF,CAAC,MAAM;wBACL9Q,OAAO,CAAC8B,GAAG,CAAC,0BAA0B,EAAEyB,QAAQ,CAAC;sBACnD;sBAEA,oBACExJ,OAAA;wBAAA+E,QAAA,gBACE/E,OAAA;0BAAA+E,QAAA,eACE/E,OAAA;4BAAK6R,SAAS,EAAC,2BAA2B;4BAAA9M,QAAA,GACvCgS,YAAY,gBACX/W,OAAA;8BACE6S,GAAG,EAAEkE,YAAa;8BAClBjE,GAAG,EAAE,aAAasC,KAAK,GAAG,CAAC,EAAG;8BAC9BvD,SAAS,EAAC,oBAAoB;8BAC9BhN,KAAK,EAAE;gCACLkO,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACdgE,SAAS,EAAE,OAAO;gCAClBrE,MAAM,EAAE;8BACV,CAAE;8BACFgB,OAAO,EAAEA,CAAA,KAAM/F,oBAAoB,CAAChB,SAAS,CAAE;8BAC/CsF,KAAK,EAAC;4BAAyB;8BAAAhN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChC,CAAC,gBAEFrF,OAAA;8BACE6R,SAAS,EAAC,qEAAqE;8BAC/EhN,KAAK,EAAE;gCACLkO,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACdY,eAAe,EAAE,SAAS;gCAC1BG,MAAM,EAAE;8BACV,CAAE;8BAAAhP,QAAA,eAEF/E,OAAA;gCAAO6R,SAAS,EAAC,YAAY;gCAAA9M,QAAA,EAAC;8BAAQ;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3C,CACN,eACDrF,OAAA;8BAAO6R,SAAS,EAAC,YAAY;8BAAA9M,QAAA,EAAEyE;4BAAQ;8BAAAtE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACLrF,OAAA;0BAAA+E,QAAA,eACE/E,OAAA;4BAAM6R,SAAS,EAAE,SAASnG,MAAM,GAAG,YAAY,GAAG,WAAW,EAAG;4BAAA3G,QAAA,EAC7D2G,MAAM,GAAG,MAAM,GAAG;0BAAU;4BAAAxG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GArCEmE,QAAQ;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsCb,CAAC;oBAET,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENrF,OAAA,CAACb,GAAG;QAAC8S,QAAQ,EAAC,OAAO;QAACC,KAAK,EAAC,iBAAiB;QAAAnN,QAAA,eAC3C/E,OAAA,CAACJ,oBAAoB;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAENrF,OAAA,CAACb,GAAG;QAAC8S,QAAQ,EAAC,aAAa;QAACC,KAAK,EAAC,aAAa;QAAAnN,QAAA,eAC7C/E,OAAA,CAACjB,IAAI;UAAAgG,QAAA,eACH/E,OAAA,CAACjB,IAAI,CAACuG,IAAI;YAAAP,QAAA,gBACR/E,OAAA;cAAA+E,QAAA,EAAI;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCrF,OAAA;cAAA+E,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJrF,OAAA;cAAA+E,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrF,OAAA;cAAA+E,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,EAAI;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCrF,OAAA;gBAAA+E,QAAA,EAAI;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BrF,OAAA;gBAAA+E,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfrF,OAAA;gBAAA+E,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAELrF,OAAA;cAAA+E,QAAA,EAAI;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BrF,OAAA;cAAA+E,QAAA,EAAG;YAIH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,EAAI;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBrF,OAAA;gBAAA+E,QAAA,EAAI;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBrF,OAAA;gBAAA+E,QAAA,EAAI;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBrF,OAAA;gBAAA+E,QAAA,EAAI;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BrF,OAAA;gBAAA+E,QAAA,EAAI;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAELrF,OAAA;cAAA+E,QAAA,EAAI;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBrF,OAAA;cAAA+E,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,EAAI;cAA2D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpErF,OAAA;gBAAA+E,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzErF,OAAA;gBAAA+E,QAAA,EAAI;cAAmE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAELrF,OAAA;cAAA+E,QAAA,EAAI;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCrF,OAAA;cAAA+E,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,EAAI;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CrF,OAAA;gBAAA+E,QAAA,EAAI;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCrF,OAAA;gBAAA+E,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDrF,OAAA;gBAAA+E,QAAA,EAAI;cAA2C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAELrF,OAAA;cAAA+E,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,gBAAI/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oCAAgC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ErF,OAAA;gBAAA+E,QAAA,gBAAI/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iDAA6C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FrF,OAAA;gBAAA+E,QAAA,gBAAI/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gDAA4C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFrF,OAAA;gBAAA+E,QAAA,gBAAI/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4DAAwD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eAELrF,OAAA;cAAK6R,SAAS,EAAC,kBAAkB;cAAA9M,QAAA,gBAC/B/E,OAAA;gBAAA+E,QAAA,gBAAI/E,OAAA;kBAAG6R,SAAS,EAAC;gBAAyB;kBAAA3M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mCAA+B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFrF,OAAA;gBAAA+E,QAAA,eAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAA6B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDrF,OAAA;gBAAI6R,SAAS,EAAC,MAAM;gBAAA9M,QAAA,gBAClB/E,OAAA;kBAAA+E,QAAA,gBAAI/E,OAAA;oBAAA+E,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gEAAkD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFrF,OAAA;kBAAA+E,QAAA,gBAAI/E,OAAA;oBAAA+E,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8EAA2D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5FrF,OAAA;kBAAA+E,QAAA,gBAAI/E,OAAA;oBAAA+E,QAAA,EAAQ;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,0EAAuD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACLrF,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+FAA2F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC,eAENrF,OAAA;cAAA+E,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BrF,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzErF,OAAA;gBAAA+E,QAAA,EAAI;cAAoD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DrF,OAAA;gBAAA+E,QAAA,EAAI;cAA4E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFrF,OAAA;gBAAA+E,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDrF,OAAA;gBAAA+E,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAELrF,OAAA;cAAA+E,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eA6CPrF,OAAA,CAACR,KAAK;MACJ6T,IAAI,EAAElQ,cAAe;MACrB8T,MAAM,EAAEA,CAAA,KAAM7T,iBAAiB,CAAC,KAAK,CAAE;MACvCoR,IAAI,EAAC,IAAI;MACT0C,QAAQ;MAAAnS,QAAA,gBAER/E,OAAA,CAACR,KAAK,CAACwF,MAAM;QAACmS,WAAW;QAAApS,QAAA,eACvB/E,OAAA,CAACR,KAAK,CAAC4X,KAAK;UAAArS,QAAA,gBACV/E,OAAA;YAAG6R,SAAS,EAAC;UAAmB;YAAA3M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B,EAAC,CAAAhC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEmG,QAAQ,kBACrCxJ,OAAA;YAAO6R,SAAS,EAAC,YAAY;YAAA9M,QAAA,GAAC,IAAE,EAAC1B,iBAAiB,CAACmG,QAAQ;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrF,OAAA,CAACR,KAAK,CAAC8F,IAAI;QAAAP,QAAA,EACR1B,iBAAiB,iBAChBrD,OAAA;UAAK6R,SAAS,EAAC,aAAa;UAAA9M,QAAA,gBAC1B/E,OAAA;YAAK6R,SAAS,EAAC,MAAM;YAAA9M,QAAA,gBACnB/E,OAAA;cAAI6R,SAAS,EAAC,MAAM;cAAA9M,QAAA,gBAClB/E,OAAA;gBAAG6R,SAAS,EAAC;cAAoB;gBAAA3M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrF,OAAA;cACE6S,GAAG,EAAExP,iBAAiB,CAACgJ,aAAc;cACrCyG,GAAG,EAAC,gBAAgB;cACpBjB,SAAS,EAAC,WAAW;cACrBhN,KAAK,EAAE;gBACLwS,SAAS,EAAE,OAAO;gBAClBxD,YAAY,EAAE,KAAK;gBACnBE,MAAM,EAAE,mBAAmB;gBAC3BG,SAAS,EAAE;cACb;YAAE;cAAAhP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLhC,iBAAiB,CAACpC,cAAc,IAAIoC,iBAAiB,CAACqI,MAAM,iBAC3D1L,OAAA;YAAK6R,SAAS,EAAC,MAAM;YAAA9M,QAAA,gBACnB/E,OAAA;cAAI6R,SAAS,EAAC,MAAM;cAAA9M,QAAA,gBAClB/E,OAAA;gBAAG6R,SAAS,EAAC;cAAoB;gBAAA3M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uCAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrF,OAAA;cACE6S,GAAG,EAAExP,iBAAiB,CAACpC,cAAe;cACtC6R,GAAG,EAAC,iBAAiB;cACrBjB,SAAS,EAAC,WAAW;cACrBhN,KAAK,EAAE;gBACLwS,SAAS,EAAE,OAAO;gBAClBxD,YAAY,EAAE,KAAK;gBACnBE,MAAM,EAAE,mBAAmB;gBAC3BG,SAAS,EAAE;cACb;YAAE;cAAAhP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EACA,CAAChC,iBAAiB,CAACqI,MAAM,iBACxB1L,OAAA;YAAK6R,SAAS,EAAC,MAAM;YAAA9M,QAAA,eACnB/E,OAAA,CAACZ,KAAK;cAACiV,OAAO,EAAC,MAAM;cAAAtP,QAAA,gBACnB/E,OAAA;gBAAG6R,SAAS,EAAC;cAAyB;gBAAA3M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,0FAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbrF,OAAA,CAACR,KAAK,CAAC8X,MAAM;QAAAvS,QAAA,eACX/E,OAAA,CAAChB,MAAM;UACLqV,OAAO,EAAC,WAAW;UACnBV,OAAO,EAAEA,CAAA,KAAMvQ,iBAAiB,CAAC,KAAK,CAAE;UAAA2B,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;;AAED;AAAAjF,EAAA,CAlnEMD,QAAQ;EAAA,QAuDSR,aAAa;AAAA;AAAA4X,EAAA,GAvD9BpX,QAAQ;AAmnEd,MAAMqX,MAAM,GAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOtH,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMuH,UAAU,GAAGvH,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAClDsH,UAAU,CAAChJ,IAAI,GAAG,UAAU;EAC5BgJ,UAAU,CAACC,SAAS,GAAGF,MAAM;EAC7BtH,QAAQ,CAACyH,IAAI,CAAChH,WAAW,CAAC8G,UAAU,CAAC;AACvC;AAEA,eAAetX,QAAQ;AAAC,IAAAoX,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}