.pavement-page {
  padding: 15px 0;
}

.webcam-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.webcam {
  width: 100%;
  max-width: 640px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview-container {
  margin-top: 15px;
  text-align: center;
}

.image-preview {
  max-width: 100%;
  max-height: 400px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.processed-image-container {
  text-align: center;
  margin-bottom: 15px;
}

.processed-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.crack-types-list {
  list-style-type: none;
  padding-left: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.crack-types-list li {
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.results-summary {
  margin-top: 15px;
}

.results-summary h5 {
  margin-top: 12px;
  margin-bottom: 8px;
  font-weight: 600;
}

/* Styles for the scrollable table */
.scrollable-table {
  max-height: 300px;
  overflow-y: auto;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.scrollable-table table {
  margin-bottom: 0;
  width: 100%;
  table-layout: auto;
}

.scrollable-table th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 1;
  white-space: nowrap;
  padding: 10px;
}

.scrollable-table td {
  padding: 8px;
  vertical-align: middle;
}

.scrollable-table th:last-child,
.scrollable-table td:last-child {
  min-width: 130px;
}

/* Ensure responsive table */
@media (max-width: 768px) {
  .scrollable-table {
    max-height: 250px;
  }
  
  .scrollable-table th {
    font-size: 0.9rem;
    padding: 8px;
  }
  
  .scrollable-table td {
    font-size: 0.85rem;
    padding: 6px;
  }
}

/* Add styles for batch processing results */
.batch-results-summary {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-top: 20px;
}

.batch-results-list {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
}

.batch-result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.batch-result-item:hover {
  background-color: #f8f9fa;
}

.batch-result-item.success {
  border-left: 3px solid #28a745;
}

.batch-result-item.error {
  border-left: 3px solid #dc3545;
}

.batch-result-filename {
  font-weight: 500;
  flex: 1;
}

.batch-result-status {
  color: #6c757d;
}

.batch-result-item.success .batch-result-status {
  color: #28a745;
}

.batch-result-item.error .batch-result-status {
  color: #dc3545;
}

/* Add styles for auto-navigation */
.batch-result-item.active {
  background-color: #e8f4ff;
  border-left-width: 5px;
}

.auto-navigation-progress {
  margin: 10px 0;
}

.progress {
  height: 10px;
  border-radius: 5px;
}

/* All Detection Results Styles */
.detection-summary-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.defect-section {
  background: white;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.defect-section.potholes {
  border-left-color: #dc3545;
}

.defect-section.cracks {
  border-left-color: #28a745;
}

.defect-section.kerbs {
  border-left-color: #007bff;
}

.defect-section h5 {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.defect-section h5 .emoji {
  margin-right: 10px;
  font-size: 1.2em;
}

.summary-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-stats .stat-item {
  padding: 10px;
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.summary-stats .stat-item:hover {
  transform: translateY(-2px);
}

.summary-stats .stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.summary-stats .stat-label {
  font-size: 0.9rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.no-defects-message {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 10px 0;
}

.model-error-alert {
  border-left: 4px solid #ffc107;
}

.total-defects-badge {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Responsive adjustments for all detection results */
@media (max-width: 768px) {
  .detection-summary-card {
    padding: 15px;
    margin: 15px 0;
  }
  
  .defect-section {
    padding: 12px;
    margin-bottom: 15px;
  }
  
  .summary-stats {
    padding: 15px;
  }
  
  .summary-stats .stat-value {
    font-size: 1.5rem;
  }
}

/* Location status styles */
.location-status {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 12px;
  margin-top: 8px;
}

.location-status .text-success {
  color: #28a745 !important;
}

.location-status .text-danger {
  color: #dc3545 !important;
}

.location-status .text-warning {
  color: #ffc107 !important;
}

.location-status .text-secondary {
  color: #6c757d !important;
}

.location-status .text-primary {
  color: #007bff !important;
  font-family: 'Courier New', monospace;
}

/* Location coordinate display styles */
.location-status .text-primary {
  font-weight: 500;
  word-break: break-all;
}

.location-status small {
  line-height: 1.4;
}

/* Different styling for current vs image location */
.location-status [data-location-type="current"] {
  opacity: 0.8;
}

.location-status [data-location-type="image"] {
  font-weight: 600;
}

/* Improve mobile layout for location status */
@media (max-width: 768px) {
  .location-status {
    padding: 8px;
    font-size: 0.875rem;
  }
  
  .location-status .alert {
    padding: 8px;
    margin-top: 8px;
  }
  
  .location-status .alert-heading {
    font-size: 0.875rem;
    margin-bottom: 4px;
  }
  
  .location-status .text-primary {
    font-size: 0.8rem;
    word-break: break-all;
  }
}

/* Sticky note reminder icon styles with animations */
.sticky-note-icon {
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  padding: 8px;
  position: relative;
  animation: gentleBounce 3s ease-in-out infinite;
}

/* Gentle bounce animation to draw attention */
@keyframes gentleBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Hover effects */
.sticky-note-icon:hover {
  transform: translateY(-4px) scale(1.05);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
  animation: none; /* Stop the bounce on hover */
}

/* Active/click effect */
.sticky-note-icon:active {
  transform: translateY(-1px) scale(1.02);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  transition: all 0.1s ease;
}

/* Icon image styles */
.sticky-note-icon img {
  display: block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
}

/* Hover effect for the image */
.sticky-note-icon:hover img {
  transform: rotate(5deg);
  filter: brightness(1.1);
}

/* Pulse effect for the icon background */
.sticky-note-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 193, 7, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  animation: pulse 2s ease-in-out infinite;
  z-index: -1;
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Wiggle animation on hover */
.sticky-note-icon:hover::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #ff6b6b;
  border-radius: 50%;
  animation: wiggle 0.5s ease-in-out infinite;
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-5deg);
  }
}

/* Custom popover styles for reminder */
.popover#reminder-popover {
  max-width: 320px !important;
}

.popover#reminder-popover .popover-header {
  background-color: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
  color: #856404;
  font-weight: 600;
}

.popover#reminder-popover .popover-body {
  color: #495057;
  line-height: 1.5;
}

.popover#reminder-popover .popover-body ul {
  margin-bottom: 0;
}

.popover#reminder-popover .popover-body li {
  margin-bottom: 4px;
}

.popover#reminder-popover .popover-body li:last-child {
  margin-bottom: 0;
}

.defect-section {
  margin-bottom: 20px;
}

.defect-section h5,
.defect-section h6 {
  margin-bottom: 10px;
  font-weight: 600;
}

.defect-section .emoji {
  margin-right: 8px;
}

/* Dynamic Color-Coded Summary Statistics Styles */
.summary-light-orange {
  background-color: rgba(255, 193, 7, 0.3) !important;
  border: 1px solid rgba(255, 193, 7, 0.5);
}

.summary-medium-orange {
  background-color: rgba(255, 193, 7, 0.6) !important;
  border: 1px solid rgba(255, 193, 7, 0.8);
}

.summary-dark-orange {
  background-color: rgba(255, 193, 7, 0.9) !important;
  border: 1px solid #ffc107;
}

/* Ensure proper contrast for text in all variations */
.summary-light-orange,
.summary-medium-orange,
.summary-dark-orange,
.bg-success,
.bg-danger {
  transition: all 0.3s ease-in-out;
}

/* Text color adjustments for better readability */
.summary-light-orange .text-success,
.summary-medium-orange .text-success {
  color: #155724 !important;
}

.summary-light-orange .text-danger,
.summary-medium-orange .text-danger {
  color: #721c24 !important;
}

.summary-light-orange .text-primary,
.summary-medium-orange .text-primary {
  color: #004085 !important;
}

.summary-dark-orange .text-success,
.bg-success .text-success,
.bg-danger .text-success {
  color: #d4edda !important;
}

.summary-dark-orange .text-danger,
.bg-success .text-danger,
.bg-danger .text-danger {
  color: #f8d7da !important;
}

.summary-dark-orange .text-primary,
.bg-success .text-primary,
.bg-danger .text-primary {
  color: #cce7ff !important;
}

/* Responsive adjustments for summary statistics */
@media (max-width: 768px) {
  .summary-light-orange,
  .summary-medium-orange,
  .summary-dark-orange,
  .bg-success,
  .bg-danger {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
}

/* Road Classification Toggle Styles */
.road-classification-control {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.road-classification-control::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
  opacity: 0.8;
}

.road-classification-control:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  border-color: #adb5bd;
  cursor: pointer;
}

.road-classification-control:hover::before {
  opacity: 1;
}

/* Toggle Button Styles */
.road-classification-control .btn {
  border: 2px solid;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.road-classification-control .btn-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  border-color: #28a745;
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.road-classification-control .btn-success:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  border-color: #1e7e34;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.road-classification-control .btn-outline-secondary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-color: #6c757d;
  color: #6c757d;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.road-classification-control .btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  border-color: #5a6268;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Status Text Styles */
.road-classification-control .text-muted {
  font-weight: 500;
  transition: color 0.3s ease;
}

.road-classification-control:hover .text-muted {
  color: #495057 !important;
}

/* Icon Styles */
.road-classification-control .info-icon {
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(108, 117, 125, 0.1);
}

.road-classification-control .info-icon:hover {
  background: rgba(108, 117, 125, 0.2);
  transform: scale(1.1);
}

/* Info Icon Wrapper */
.info-icon-wrapper {
  display: inline-block;
  margin-left: 8px;
  position: relative;
}

/* Road Classification Info Icon - Maximum Visibility */
.road-classification-info-icon {
  color: #007bff !important;
  font-size: 14px !important;
  cursor: help !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  opacity: 1 !important;
  transition: all 0.2s ease !important;
  background: rgba(0, 123, 255, 0.1) !important;
  border-radius: 50% !important;
  padding: 0 !important;
  width: 22px !important;
  height: 22px !important;
  text-align: center !important;
  line-height: 1 !important;
  border: 1px solid rgba(0, 123, 255, 0.3) !important;
  box-sizing: border-box !important;
  font-weight: bold !important;
  font-family: Arial, sans-serif !important;
}

.road-classification-info-icon:hover {
  color: #0056b3 !important;
  background: rgba(0, 123, 255, 0.2) !important;
  border-color: rgba(0, 123, 255, 0.5) !important;
  transform: scale(1.1) !important;
  filter: drop-shadow(0 2px 4px rgba(0, 123, 255, 0.3)) !important;
  font-weight: bold !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .road-classification-control {
    padding: 10px 12px;
    margin-top: 8px;
  }

  .road-classification-control .btn {
    min-width: 50px;
    font-size: 11px;
    padding: 4px 12px;
  }

  .road-classification-control small {
    font-size: 10px !important;
  }
}

/* Animation for toggle state change */
.road-classification-control .btn {
  position: relative;
}

.road-classification-control .btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.road-classification-control .btn:active::after {
  width: 100px;
  height: 100px;
}

/* Image thumbnail styles for detection table */
.image-thumbnail-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.image-thumbnail-container:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-thumbnail {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
  transition: border-color 0.2s ease;
}

.image-thumbnail-container:hover .image-thumbnail {
  border-color: #007bff;
}

.thumbnail-overlay {
  position: absolute;
  top: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.image-thumbnail-container:hover .thumbnail-overlay {
  opacity: 1;
  background: rgba(0, 123, 255, 0.9);
}

/* Tooltip styles for image thumbnails */
.image-thumbnail-container[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

/* Enhanced modal styles */
.modal-body img {
  transition: transform 0.3s ease;
}

.modal-body img:hover {
  transform: scale(1.02);
}

/* Enhanced detection table styles */
.detection-table-container {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detection-table-container table {
  margin-bottom: 0;
  font-size: 0.9rem;
}

.detection-table-container th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 10;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  white-space: nowrap;
  padding: 12px 8px;
  vertical-align: middle;
}

.detection-table-container td {
  padding: 10px 8px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

.detection-table-container tbody tr:hover {
  background-color: #f8f9fa;
}

/* Responsive table adjustments */
@media (max-width: 1200px) {
  .detection-table-container {
    max-height: 500px;
  }

  .detection-table-container th,
  .detection-table-container td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .detection-table-container {
    max-height: 400px;
  }

  .detection-table-container th,
  .detection-table-container td {
    padding: 6px 4px;
    font-size: 0.8rem;
  }

  .image-thumbnail {
    width: 60px !important;
    height: 45px !important;
  }

  .thumbnail-overlay {
    width: 12px !important;
    height: 12px !important;
    font-size: 8px !important;
  }
}

/* Smooth scrolling for table */
.detection-table-container {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.detection-table-container::-webkit-scrollbar {
  width: 8px;
}

.detection-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.detection-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.detection-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
